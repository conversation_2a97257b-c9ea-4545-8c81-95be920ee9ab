-- 三角洲行动定制 - 循环控制模块
-- 版本: 1.0.0
-- 功能: 提供可控的循环执行框架，包括超时、迭代控制、条件退出等功能

local LoopController = {}

-- 获取全局框架引用（如果可用）
local GameScript = _G.GameScript
local Logger = _G.Logger

-- 循环控制器版本
LoopController.version = "1.0.0"

-- 默认配置
LoopController.DEFAULT_CONFIG = {
    max_iterations = 100,       -- 最大迭代次数
    timeout_seconds = 300,      -- 超时时间（默认5分钟）
    delay_ms = 100,             -- 每次迭代的延迟（毫秒）
    break_on_error = true,      -- 出错时是否中断循环
    auto_catch_errors = true,   -- 是否自动捕获循环体中的错误
    log_level = "INFO",         -- 日志级别：DEBUG, INFO, WARN, ERROR
    log_interval = 10,          -- 每隔多少次迭代记录一次日志
}

-- 创建一个受控循环
function LoopController.createControlledLoop(config)
    -- 合并配置
    local cfg = {}
    for k, v in pairs(LoopController.DEFAULT_CONFIG) do
        cfg[k] = v
    end
    
    if config then
        for k, v in pairs(config) do
            cfg[k] = v
        end
    end
    
    -- 创建循环控制对象
    return {
        -- 配置参数
        max_iterations = cfg.max_iterations,
        timeout_seconds = cfg.timeout_seconds,
        delay_ms = cfg.delay_ms,
        break_on_error = cfg.break_on_error,
        auto_catch_errors = cfg.auto_catch_errors,
        log_level = cfg.log_level,
        log_interval = cfg.log_interval,
        break_conditions = config.break_conditions or {},
        
        -- 回调函数
        on_iteration = config.on_iteration,
        on_timeout = config.on_timeout,
        on_break = config.on_break,
        on_error = config.on_error,
        on_complete = config.on_complete,
        
        -- 内部状态
        current_iteration = 0,
        start_time = 0,
        elapsed_time = 0,
        is_running = false,
        result = nil,
        
        -- 统计信息
        stats = {
            total_iterations = 0,
            error_count = 0,
            last_error = nil,
            break_reason = nil
        }
    }
end

-- 记录循环日志
local function logLoopEvent(loop_obj, level, message, data)
    if not Logger then return end
    
    -- 确保日志级别有效
    local log_level = (loop_obj.log_level or "INFO"):upper()
    
    -- 调用对应的日志函数
    if level == "DEBUG" and Logger.debug then
        Logger.debug("LoopController", message, data)
    elseif level == "INFO" and Logger.info then
        Logger.info("LoopController", message, data)
    elseif level == "WARN" and Logger.warn then
        Logger.warn("LoopController", message, data)
    elseif level == "ERROR" and Logger.error then
        Logger.error("LoopController", message, data)
    end
end

-- 执行受控循环
function LoopController.execute(loop_obj, loop_body)
    if not loop_body then
        logLoopEvent(loop_obj, "ERROR", "无效的循环体函数", {error = "循环体函数不能为空"})
        return 0, "INVALID_BODY"
    end
    
    -- 重置/初始化循环状态
    loop_obj.is_running = true
    loop_obj.start_time = os.time()
    loop_obj.current_iteration = 0
    loop_obj.result = nil
    loop_obj.stats.total_iterations = 0
    loop_obj.stats.error_count = 0
    loop_obj.stats.last_error = nil
    loop_obj.stats.break_reason = nil
    
    logLoopEvent(loop_obj, "INFO", "开始执行受控循环", {
        max_iterations = loop_obj.max_iterations,
        timeout_seconds = loop_obj.timeout_seconds
    })
    
    -- 执行循环
    while loop_obj.is_running do
        loop_obj.current_iteration = loop_obj.current_iteration + 1
        loop_obj.stats.total_iterations = loop_obj.stats.total_iterations + 1
        loop_obj.elapsed_time = os.time() - loop_obj.start_time
        
        -- 检查最大迭代次数
        if loop_obj.current_iteration > loop_obj.max_iterations then
            loop_obj.stats.break_reason = "MAX_ITERATIONS"
            
            if loop_obj.on_break then
                loop_obj.on_break("MAX_ITERATIONS", loop_obj.current_iteration)
            end
            
            logLoopEvent(loop_obj, "INFO", "达到最大迭代次数，循环终止", {
                iterations = loop_obj.current_iteration,
                max_iterations = loop_obj.max_iterations
            })
            
            break
        end
        
        -- 检查超时
        if loop_obj.elapsed_time > loop_obj.timeout_seconds then
            loop_obj.stats.break_reason = "TIMEOUT"
            
            if loop_obj.on_timeout then
                loop_obj.on_timeout(loop_obj.elapsed_time)
            end
            
            logLoopEvent(loop_obj, "WARN", "循环执行超时", {
                elapsed_seconds = loop_obj.elapsed_time,
                timeout_seconds = loop_obj.timeout_seconds
            })
            
            break
        end
        
        -- 检查自定义退出条件
        for index, condition in ipairs(loop_obj.break_conditions) do
            local success, result = pcall(condition)
            
            if success and result then
                loop_obj.stats.break_reason = "CUSTOM_CONDITION"
                
                if loop_obj.on_break then
                    loop_obj.on_break("CUSTOM_CONDITION_" .. index, loop_obj.current_iteration)
                end
                
                logLoopEvent(loop_obj, "INFO", "满足自定义退出条件，循环终止", {
                    condition_index = index,
                    iterations = loop_obj.current_iteration
                })
                
                loop_obj.is_running = false
                break
            end
        end
        
        -- 如果在检查条件时已经停止，则跳出
        if not loop_obj.is_running then
            break
        end
        
        -- 执行循环体
        local success, result
        
        if loop_obj.auto_catch_errors then
            success, result = pcall(loop_body, loop_obj.current_iteration, loop_obj.elapsed_time)
            
            if not success then
                loop_obj.stats.error_count = loop_obj.stats.error_count + 1
                loop_obj.stats.last_error = result
                
                logLoopEvent(loop_obj, "ERROR", "循环体执行出错", {
                    error = tostring(result),
                    iteration = loop_obj.current_iteration
                })
                
                if loop_obj.on_error then
                    loop_obj.on_error(result, loop_obj.current_iteration)
                end
                
                if loop_obj.break_on_error then
                    loop_obj.stats.break_reason = "ERROR"
                    loop_obj.is_running = false
                    break
                end
                
                -- 错误情况下，跳过本次迭代的其余部分
                if loop_obj.delay_ms and loop_obj.delay_ms > 0 then
                    sleep(loop_obj.delay_ms)
                end
                goto continue
            end
        else
            -- 不自动捕获错误，直接执行
            result = loop_body(loop_obj.current_iteration, loop_obj.elapsed_time)
        end
        
        loop_obj.result = result
        
        -- 如果循环体返回 false，则停止循环
        if result == false then
            loop_obj.stats.break_reason = "BODY_RETURNED_FALSE"
            
            logLoopEvent(loop_obj, "INFO", "循环体返回false，循环终止", {
                iterations = loop_obj.current_iteration
            })
            
            break
        end
        
        -- 迭代回调
        if loop_obj.on_iteration then
            -- 包装回调，防止异常
            pcall(function()
                loop_obj.on_iteration(loop_obj.current_iteration, loop_obj.elapsed_time, result)
            end)
        end
        
        -- 定期日志
        if loop_obj.log_interval > 0 and loop_obj.current_iteration % loop_obj.log_interval == 0 then
            logLoopEvent(loop_obj, "DEBUG", "循环进行中", {
                iteration = loop_obj.current_iteration,
                elapsed_seconds = loop_obj.elapsed_time
            })
        end
        
        -- 延迟，防止CPU占用过高
        if loop_obj.delay_ms and loop_obj.delay_ms > 0 then
            sleep(loop_obj.delay_ms)
        end
        
        ::continue::
    end
    
    -- 循环完成回调
    if loop_obj.on_complete then
        pcall(loop_obj.on_complete, loop_obj.stats.total_iterations, loop_obj.elapsed_time, loop_obj.result)
    end
    
    loop_obj.is_running = false
    
    -- 记录最终日志
    logLoopEvent(loop_obj, "INFO", "循环执行完成", {
        total_iterations = loop_obj.stats.total_iterations,
        elapsed_seconds = loop_obj.elapsed_time,
        break_reason = loop_obj.stats.break_reason,
        error_count = loop_obj.stats.error_count
    })
    
    return loop_obj.stats.total_iterations, loop_obj.stats.break_reason, loop_obj.result
end

-- 手动停止循环（可在回调中调用）
function LoopController.stop(loop_obj, reason)
    if loop_obj and loop_obj.is_running then
        loop_obj.is_running = false
        loop_obj.stats.break_reason = reason or "MANUAL_STOP"
        return true
    end
    return false
end

-- =================== 预定义循环模式 ===================

-- 创建价格检查循环（用于扫货等场景）
function LoopController.createPriceCheckLoop(target_price, ocr_func, buy_func, config)
    -- 合并配置
    local cfg = config or {}
    local success_count = 0
    local ocr_fail_count = 0
    
    local loop = LoopController.createControlledLoop({
        max_iterations = cfg.max_iterations or 50,
        timeout_seconds = cfg.timeout_seconds or 180, -- 3分钟超时
        delay_ms = cfg.delay_ms or 500,
        log_interval = cfg.log_interval or 5,
        break_conditions = {
            function() return _G.资金不足 == true end,  -- 资金不足时退出
            function() return ocr_fail_count >= 10 end   -- OCR连续失败10次退出
        },
        on_timeout = function(elapsed)
            logLoopEvent(loop, "WARN", "价格检查循环超时", {
                elapsed_seconds = elapsed,
                target_price = target_price
            })
        end,
        on_break = function(reason, iteration)
            logLoopEvent(loop, "INFO", "价格检查提前终止", {
                reason = reason,
                iteration = iteration,
                success_count = success_count
            })
        end,
        on_complete = function(total_iterations, elapsed_time)
            logLoopEvent(loop, "INFO", "价格检查完成", {
                total_iterations = total_iterations,
                elapsed_seconds = elapsed_time,
                success_count = success_count
            })
        end
    })
    
    -- 设置循环体
    return LoopController.execute(loop, function(iteration)
        -- OCR 识别价格
        local price_str = ocr_func()
        local price = tonumber(price_str)
        
        if not price then
            ocr_fail_count = ocr_fail_count + 1
            logLoopEvent(loop, "DEBUG", "价格识别失败", {
                iteration = iteration,
                fail_count = ocr_fail_count
            })
            return true -- 继续循环
        end
        
        ocr_fail_count = 0 -- 重置失败计数
        
        if price < target_price then
            logLoopEvent(loop, "INFO", "检测到符合条件的价格", {
                detected_price = price,
                target_price = target_price
            })
            
            local buy_success = buy_func(price)
            if buy_success then
                success_count = success_count + 1
                logLoopEvent(loop, "INFO", "购买成功", {
                    price = price,
                    success_count = success_count
                })
            end
        else
            logLoopEvent(loop, "DEBUG", "价格不符合条件", {
                detected_price = price,
                target_price = target_price
            })
        end
        
        return true -- 继续循环
    end), success_count
end

-- 创建界面元素等待循环（用于等待UI元素出现）
function LoopController.createWaitForElementLoop(element_finder_func, config)
    local cfg = config or {}
    local result = nil
    
    local loop = LoopController.createControlledLoop({
        max_iterations = cfg.max_iterations or 30,
        timeout_seconds = cfg.timeout_seconds or 10,
        delay_ms = cfg.delay_ms or 300,
        log_level = "DEBUG",
        on_timeout = function(elapsed)
            if cfg.description then
                logLoopEvent(loop, "WARN", "等待元素超时: " .. cfg.description, {
                    elapsed_seconds = elapsed
                })
            end
        end
    })
    
    LoopController.execute(loop, function()
        local success, element_found, extra_data = pcall(element_finder_func)
        
        if not success then
            return true -- 发生错误但继续尝试
        end
        
        if element_found then
            result = extra_data or true
            return false -- 找到元素，退出循环
        end
        
        return true -- 继续循环
    end)
    
    return result
end

-- =================== 与GameScript框架集成 ===================
-- 如果GameScript可用，注册到GameScript
if GameScript then
    GameScript.modules.LoopController = LoopController
    if GameScript.registerGlobal then
        GameScript.registerGlobal("LoopController", LoopController, "核心框架")
    end
end

return LoopController 