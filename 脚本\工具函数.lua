--[[
工具函数库
包含常用的时间处理和界面导航函数
]]--

-- 【修复】移除循环依赖，使用延迟加载
-- local 自动重启游戏 = require("自动重启游戏") -- 移除直接依赖

--[[
获取当前小时
功能：获取系统当前小时数（24小时制）
返回：0-23的整数
]]--
function 获取当前小时()
    local 时间 = os.date("*t")
    return tonumber(时间.hour)
end

--[[
获取当前分钟
功能：获取系统当前分钟数
返回：0-59的整数
]]--
function 获取当前分钟()
    local 时间 = os.date("*t")
    return tonumber(时间.min)
end

--[[
在时间段内
功能：判断当前时间是否在指定的时间段内
参数：
- 当前小时：当前的小时数
- 开始时间：时间段的开始小时
- 结束时间：时间段的结束小时
返回：true/false
]]--
function 在时间段内(当前小时, 开始时间, 结束时间)
    -- 如果开始时间小于结束时间，表示时间段在同一天内
    if 开始时间 < 结束时间 then
        return 当前小时 >= 开始时间 and 当前小时 < 结束时间
    -- 如果开始时间大于结束时间，表示时间段跨越午夜
    elseif 开始时间 > 结束时间 then
        return 当前小时 >= 开始时间 or 当前小时 < 结束时间
    -- 如果开始时间等于结束时间，表示整整24小时
    else
        return true
    end
end

--[[
尝试返回大厅
功能：通用的返回大厅功能
特点：
- 支持多种返回按钮的识别
- 有固定坐标点击作为备选方案
- 最多尝试7次
- 提供详细的显示信息输出
参数：
- log_prefix：显示信息前缀
返回：是否成功返回大厅
]]--
function 尝试返回大厅(log_prefix)
    log_prefix = log_prefix or "[返回逻辑]"
    local 返回大厅尝试次数 = 0
    local 最大返回尝试 = 7 -- 尝试次数可以根据需要调整
    
    -- 循环尝试返回大厅
    while 返回大厅尝试次数 < 最大返回尝试 do
        返回大厅尝试次数 = 返回大厅尝试次数 + 1
        
        -- 检查是否已在大厅（通过右上角图标判断）
        local idx, x, y = findPic(1192, 0, 1279, 112, "到时间_返回大厅.png", "101010", 0, 0.7)
        
        -- 成功返回到大厅
        if x > -1 and y > -1 then
            sleep(1000)
            return true -- 成功返回
        else
            local acted_this_return_loop = false
            
            -- 尝试点击左上角返回按钮（多个场景通用）
            local idx_return, x_return, y_return = findPic(0, 0, 165, 72, "领取邮件_交易行返回大厅.png|领取邮件_交易行返回大厅1.png|特勤处_返回特勤处大厅.png", "101010", 0, 0.7)
            if x_return > -1 and y_return > -1 then
                tap(x_return, y_return)
                sleep(2000)
                acted_this_return_loop = true
            end
            
            -- 如果没找到返回按钮，尝试点击固定坐标
            if not acted_this_return_loop then
                tap(91, 31) -- 通用返回按钮位置
                sleep(2000)
                acted_this_return_loop = true
            end
        end
        sleep(1000) -- 每次尝试后等待
    end
    
    显示信息(log_prefix .. " ✗ 达到最大返回大厅尝试次数，可能未成功返回。", false)
    

    显示信息("⚠️ 连续返回大厅失败，根据设置不执行重启。", false)
    
    return false -- 返回失败
end

-- 创建目录（支持多级目录）
-- 功能：创建指定路径的目录（自动创建缺失的父级目录）
-- 参数：dir_path - 要创建的目录路径（如"/data/logs"）
-- 返回：true-成功，false-失败
function makeDir(dir_path)
    local lfs = require 'lfs'
    local path = ''
    for folder in string.gmatch(dir_path, '[^/\\]+') do
        path = path .. folder .. '/'
        if lfs.attributes(path) then
            if lfs.attributes(path, 'mode') ~= 'directory' then
                return false -- 路径存在但不是目录
            end
        else
            if not lfs.mkdir(path) then
                return false -- 创建目录失败
            end
        end
    end
    return true
end

-- 【性能优化】配置管理相关函数 - 使用缓存和预计算路径
local 配置缓存 = {
    配置目录路径 = nil,

    -- 初始化配置目录（只执行一次）
    初始化配置目录 = function(self)
        if not self.配置目录路径 then
            local 存储路径 = getWorkPath()
            self.配置目录路径 = 存储路径 .. "/三角洲行动定制/"
            -- 确保目录存在
            makeDir(self.配置目录路径)
        end
        return self.配置目录路径
    end
}

-- 优化后的获取配置文件路径函数
local function 获取配置文件路径(文件名)
    local 配置目录 = 配置缓存:初始化配置目录()
    return 配置目录 .. 文件名
end

-- 检查文件是否存在
local function 文件是否存在(路径)
    local file = io.open(路径, "r")
    if file then
        file:close()
        return true
    end
    return false
end

-- 【注意】卡密读取函数已移至网络验证模块统一管理，此处不再重复定义
-- 如需使用卡密功能，请调用网络验证模块中的 保存卡密() 和 读取卡密() 函数

-- 【性能优化】保存三角洲UI配置
function 保存三角洲配置(配置数据)
    local 文件路径 = 获取配置文件路径("triangle_settings.json")

    local file = io.open(文件路径, "w")
    if file then
        local json_str = jsonLib.encode(配置数据)
        file:write(json_str)
        file:close()

        -- 简化验证：只检查文件是否存在
        return 文件是否存在(文件路径)
    end
    return false
end

-- 【性能优化】读取三角洲UI配置 - 减少调试输出，优化错误处理
function 读取三角洲配置()
    local 文件路径 = 获取配置文件路径("triangle_settings.json")

    if not 文件是否存在(文件路径) then
        return 获取默认配置()
    end

    local file = io.open(文件路径, "r")
    if file then
        local content = file:read("*all")
        file:close()

        if content and content ~= "" then
            local ok, 配置 = pcall(jsonLib.decode, content)
            if ok and type(配置) == "table" then
                return 配置
            end
        end
    end

    -- 任何错误情况都返回默认配置
    return 获取默认配置()
end

-- 【性能优化】获取默认配置 - 移除调试输出
function 获取默认配置()
    return {
        ["多选框_选择扫货功能"] = false,
        ["多选框_出售子弹"] = false,
        ["子弹类型"] = 0,  -- 0代表9*19，1代表5.7*28
        ["多选框_滚仓扫货"] = false,
        ["扫货_自动挂机"] = false,
        ["特勤处_制作装备"] = false,
        ["输入框_扫货输入价格"] = "",
        ["输入框_抢购次数"] = "",
        ["输入框_出售价格"] = "",
        ["滚仓_子弹数量"] = "",
        ["滚仓_子弹价格"] = "",
        ["出售_开始时间"] = "13",
        ["出售_结束时间"] = "19"
    }
end

-- 【性能优化】保存用户配置 - 减少调试输出
function 保存用户配置(配置数据)
    local file = io.open(获取配置文件路径("user_settings.json"), "w")
    if file then
        local json_str = jsonLib.encode(配置数据)
        file:write(json_str)
        file:close()
        return true
    end
    return false
end

-- 读取用户配置
function 读取用户配置()
    local file = io.open(获取配置文件路径("user_settings.json"), "r")
    if file then
        local content = file:read("*all")
        file:close()
        if content and content ~= "" then
            local ok, 配置 = pcall(jsonLib.decode, content)
            if ok then
                print("成功读取用户配置")
                return 配置
            end
        end
    end
    print("读取用户配置失败，使用默认配置")
    return {
        自动抢购 = true,
        自动出售 = true,
        自动重启 = true,
        显示提示 = true,
        特勤处制造 = false
    }
end


-- =================== 配置管理模块 ===================
-- 【新增】配置管理核心模块 - 支持多配置文件管理
local 配置管理模块 = {
    配置目录 = nil,
    配置文件扩展名 = ".json",

    -- 初始化配置目录
    初始化目录 = function(self)
        if not self.配置目录 then
            local 存储路径 = getWorkPath()
            self.配置目录 = 存储路径 .. "/三角洲行动定制/configs/"
            makeDir(self.配置目录)
        end
        return self.配置目录
    end,

    -- 获取配置文件完整路径
    获取配置路径 = function(self, 配置名称)
        local 目录 = self:初始化目录()
        return 目录 .. 配置名称 .. self.配置文件扩展名
    end,

    -- 获取默认配置结构
    获取默认配置 = function(self)
        return {
            -- 配置元信息
            配置信息 = {
                名称 = "",
                创建时间 = os.time(),
                修改时间 = os.time(),
                版本 = "1.0",
                描述 = ""
            },

            -- 功能选择配置
            功能配置 = {
                多选框_选择扫货功能 = false,
                多选框_选择房卡功能 = false,
                多选框_选择五级蛋 = false,
                多选框_出售子弹 = false,
                多选框_单机抢购模式 = false,
                扫货_自动挂机 = false,
                特勤处_制作装备 = false,
                子弹类型 = 0
            },

            -- 价格配置
            价格配置 = {
                输入框_扫货输入价格 = "630",
                输入框_抢购次数 = "1",
                输入框_单机抢购价格 = "630",
                输入框_单机抢购延迟 = "30",
                输入框_资金保护 = "500000",
                输入框_房卡输入价格 = "",
                输入框_五级蛋价格 = "150000",
                输入框_出售价格 = "758"
            },

            -- 时间配置
            时间配置 = {
                出售_开始时间 = "13",
                出售_结束时间 = "19"
            }
        }
    end,

    -- 验证配置数据结构
    验证配置数据 = function(self, 配置数据)
        if type(配置数据) ~= "table" then
            return false, "配置数据必须是表格类型"
        end

        -- 检查必需的配置节
        local 必需节 = {"配置信息", "功能配置", "价格配置", "时间配置"}
        for _, 节名 in ipairs(必需节) do
            if not 配置数据[节名] or type(配置数据[节名]) ~= "table" then
                return false, "缺少必需的配置节: " .. 节名
            end
        end

        -- 验证功能配置
        local 功能配置 = 配置数据.功能配置
        local 有效功能 = {
            "多选框_选择扫货功能", "多选框_选择房卡功能", "多选框_选择五级蛋",
            "多选框_出售子弹", "多选框_单机抢购模式", "扫货_自动挂机", "特勤处_制作装备"
        }

        for _, 功能名 in ipairs(有效功能) do
            if 功能配置[功能名] ~= nil and type(功能配置[功能名]) ~= "boolean" then
                return false, "功能配置 " .. 功能名 .. " 必须是布尔值"
            end
        end

        -- 验证价格配置
        local 价格配置 = 配置数据.价格配置
        local 价格字段 = {
            "输入框_扫货输入价格", "输入框_抢购次数", "输入框_单机抢购价格",
            "输入框_单机抢购延迟", "输入框_资金保护", "输入框_房卡输入价格",
            "输入框_五级蛋价格", "输入框_出售价格"
        }

        for _, 字段名 in ipairs(价格字段) do
            if 价格配置[字段名] and type(价格配置[字段名]) ~= "string" then
                return false, "价格配置 " .. 字段名 .. " 必须是字符串类型"
            end
        end

        return true, "配置数据验证通过"
    end,

    -- 清理配置名称
    清理配置名称 = function(self, 配置名称)
        if not 配置名称 or type(配置名称) ~= "string" then
            return nil, "配置名称必须是非空字符串"
        end

        -- 移除首尾空格
        配置名称 = string.gsub(配置名称, "^%s*(.-)%s*$", "%1")

        if 配置名称 == "" then
            return nil, "配置名称不能为空"
        end

        -- 移除非法字符
        配置名称 = string.gsub(配置名称, "[<>:\"/\\|?*]", "_")

        -- 限制长度
        if #配置名称 > 50 then
            配置名称 = string.sub(配置名称, 1, 50)
        end

        return 配置名称, "配置名称清理完成"
    end,

    -- 保存配置到文件（增强版）
    保存配置 = function(self, 配置名称, 配置数据)
        -- 清理和验证配置名称
        local 清理后名称, 清理错误 = self:清理配置名称(配置名称)
        if not 清理后名称 then
            return false, 清理错误
        end

        -- 验证配置数据
        local 验证成功, 验证错误 = self:验证配置数据(配置数据)
        if not 验证成功 then
            return false, "配置数据验证失败: " .. 验证错误
        end

        local 文件路径 = self:获取配置路径(清理后名称)

        -- 检查目录权限
        local 目录 = self:初始化目录()
        if not 目录 then
            return false, "无法创建配置目录"
        end

        -- 更新配置信息
        if not 配置数据.配置信息 then
            配置数据.配置信息 = {}
        end
        配置数据.配置信息.名称 = 清理后名称
        配置数据.配置信息.修改时间 = os.time()
        if not 配置数据.配置信息.创建时间 then
            配置数据.配置信息.创建时间 = os.time()
        end
        配置数据.配置信息.版本 = "1.0"

        -- 尝试保存文件
        local file = io.open(文件路径, "w")
        if file then
            local ok, json_str = pcall(jsonLib.encode, 配置数据)
            if ok then
                file:write(json_str)
                file:close()

                -- 验证保存是否成功
                local 验证文件 = io.open(文件路径, "r")
                if 验证文件 then
                    local 内容 = 验证文件:read("*all")
                    验证文件:close()
                    if 内容 and #内容 > 0 then
                        return true, "配置保存成功"
                    else
                        return false, "配置文件保存后为空"
                    end
                else
                    return false, "配置文件保存验证失败"
                end
            else
                file:close()
                return false, "配置序列化失败: " .. tostring(json_str)
            end
        else
            return false, "无法创建配置文件: " .. 文件路径
        end
    end,

    -- 从文件加载配置（增强版）
    加载配置 = function(self, 配置名称)
        -- 清理和验证配置名称
        local 清理后名称, 清理错误 = self:清理配置名称(配置名称)
        if not 清理后名称 then
            return nil, 清理错误
        end

        local 文件路径 = self:获取配置路径(清理后名称)

        -- 检查文件是否存在
        local file = io.open(文件路径, "r")
        if not file then
            return nil, "配置文件不存在: " .. 清理后名称
        end

        -- 读取文件内容
        local content = file:read("*all")
        file:close()

        if not content or content == "" then
            return nil, "配置文件为空"
        end

        -- 解析JSON
        local ok, 配置数据 = pcall(jsonLib.decode, content)
        if not ok then
            return nil, "配置文件JSON格式错误: " .. tostring(配置数据)
        end

        if type(配置数据) ~= "table" then
            return nil, "配置文件内容格式错误，必须是表格类型"
        end

        -- 验证配置数据结构
        local 验证成功, 验证错误 = self:验证配置数据(配置数据)
        if not 验证成功 then
            return nil, "配置数据结构验证失败: " .. 验证错误
        end

        -- 检查配置版本兼容性
        if 配置数据.配置信息 and 配置数据.配置信息.版本 then
            local 版本 = 配置数据.配置信息.版本
            if 版本 ~= "1.0" then
                return nil, "配置文件版本不兼容: " .. 版本
            end
        end

        return 配置数据, "配置加载成功"
    end,

    -- 删除配置文件（增强版）
    删除配置 = function(self, 配置名称)
        -- 清理和验证配置名称
        local 清理后名称, 清理错误 = self:清理配置名称(配置名称)
        if not 清理后名称 then
            return false, 清理错误
        end

        local 文件路径 = self:获取配置路径(清理后名称)

        -- 检查文件是否存在
        local file = io.open(文件路径, "r")
        if not file then
            return false, "配置文件不存在: " .. 清理后名称
        end
        file:close()

        -- 创建备份（可选）
        local 备份路径 = 文件路径 .. ".bak"
        local 备份命令 = string.format('copy "%s" "%s" >nul 2>&1', 文件路径, 备份路径)
        os.execute(备份命令)

        -- 尝试删除文件
        local 删除命令 = string.format('del "%s" >nul 2>&1', 文件路径)
        local 结果 = os.execute(删除命令)

        -- 验证删除是否成功
        local 验证文件 = io.open(文件路径, "r")
        if 验证文件 then
            验证文件:close()
            -- 删除失败，恢复备份
            local 恢复命令 = string.format('move "%s" "%s" >nul 2>&1', 备份路径, 文件路径)
            os.execute(恢复命令)
            return false, "配置删除失败，文件仍然存在"
        else
            -- 删除成功，清理备份
            local 清理备份命令 = string.format('del "%s" >nul 2>&1', 备份路径)
            os.execute(清理备份命令)
            return true, "配置删除成功"
        end
    end,

    -- 获取所有配置文件列表（增强版）
    获取配置列表 = function(self)
        local 目录 = self:初始化目录()
        local 配置列表 = {}
        local 配置详情 = {}

        -- 使用ls命令获取文件列表（适用于Android）
        local 命令 = string.format('ls "%s"*.json 2>/dev/null', 目录)
        local 句柄 = io.popen(命令)
        if 句柄 then
            for 文件路径 in 句柄:lines() do
                -- 提取文件名并移除扩展名
                local 配置名 = string.match(文件路径, "([^/]+)%.json$")
                if 配置名 then
                    -- 验证配置文件是否有效
                    local 配置数据, 错误信息 = self:加载配置(配置名)
                    if 配置数据 then
                        table.insert(配置列表, 配置名)
                        配置详情[配置名] = {
                            名称 = 配置名,
                            创建时间 = 配置数据.配置信息 and 配置数据.配置信息.创建时间 or 0,
                            修改时间 = 配置数据.配置信息 and 配置数据.配置信息.修改时间 or 0,
                            版本 = 配置数据.配置信息 and 配置数据.配置信息.版本 or "未知",
                            有效 = true
                        }
                    else
                        -- 记录无效的配置文件
                        配置详情[配置名] = {
                            名称 = 配置名,
                            错误信息 = 错误信息,
                            有效 = false
                        }
                    end
                end
            end
            句柄:close()
        else
            -- 如果ls命令失败，尝试直接遍历目录
            local 测试配置名列表 = {"默认配置", "测试配置", "123", "ces", "扫货配置", "房卡配置"}
            for _, 配置名 in ipairs(测试配置名列表) do
                local 文件路径 = self:获取配置路径(配置名)
                local file = io.open(文件路径, "r")
                if file then
                    file:close()
                    -- 验证配置文件是否有效
                    local 配置数据, 错误信息 = self:加载配置(配置名)
                    if 配置数据 then
                        table.insert(配置列表, 配置名)
                        配置详情[配置名] = {
                            名称 = 配置名,
                            创建时间 = 配置数据.配置信息 and 配置数据.配置信息.创建时间 or 0,
                            修改时间 = 配置数据.配置信息 and 配置数据.配置信息.修改时间 or 0,
                            版本 = 配置数据.配置信息 and 配置数据.配置信息.版本 or "未知",
                            有效 = true
                        }
                    end
                end
            end
        end

        -- 按修改时间排序（最新的在前）
        table.sort(配置列表, function(a, b)
            local 时间a = 配置详情[a] and 配置详情[a].修改时间 or 0
            local 时间b = 配置详情[b] and 配置详情[b].修改时间 or 0
            return 时间a > 时间b
        end)

        return 配置列表, 配置详情
    end,

    -- 获取配置统计信息
    获取配置统计 = function(self)
        local 配置列表, 配置详情 = self:获取配置列表()
        local 统计 = {
            总数 = #配置列表,
            有效数 = 0,
            无效数 = 0,
            最新修改时间 = 0,
            最旧创建时间 = os.time()
        }

        for _, 配置名 in ipairs(配置列表) do
            local 详情 = 配置详情[配置名]
            if 详情 and 详情.有效 then
                统计.有效数 = 统计.有效数 + 1
                if 详情.修改时间 > 统计.最新修改时间 then
                    统计.最新修改时间 = 详情.修改时间
                end
                if 详情.创建时间 < 统计.最旧创建时间 then
                    统计.最旧创建时间 = 详情.创建时间
                end
            else
                统计.无效数 = 统计.无效数 + 1
            end
        end

        return 统计
    end
}

-- =================== 配置管理全局函数 ===================
-- 【新增】全局配置管理函数，供主脚本调用

-- 从UI界面提取当前配置
function 从UI提取当前配置(界面配置)
    local ui_page0 = 界面配置.page0 or {}
    local ui_page1 = 界面配置.page1 or {}
    local ui_page2 = 界面配置.page2 or {}  -- 配置管理页面

    local 配置数据 = 配置管理模块:获取默认配置()

    -- 提取功能配置
    配置数据.功能配置.多选框_选择扫货功能 = ui_page0.多选框_选择扫货功能 or false
    配置数据.功能配置.多选框_选择房卡功能 = ui_page0.多选框_选择房卡功能 or false
    配置数据.功能配置.多选框_选择五级蛋 = ui_page0.多选框_选择五级蛋 or false
    配置数据.功能配置.多选框_出售子弹 = ui_page0.多选框_出售子弹 or false
    配置数据.功能配置.多选框_单机抢购模式 = ui_page0.多选框_单机抢购模式 or false
    配置数据.功能配置.扫货_自动挂机 = ui_page0.扫货_自动挂机 or false
    配置数据.功能配置.特勤处_制作装备 = ui_page0.特勤处_制作装备 or false
    配置数据.功能配置.子弹类型 = ui_page0.子弹类型 or 0

    -- 提取价格配置
    配置数据.价格配置.输入框_扫货输入价格 = ui_page1.输入框_扫货输入价格 or "630"
    配置数据.价格配置.输入框_抢购次数 = ui_page1.输入框_抢购次数 or "1"
    配置数据.价格配置.输入框_单机抢购价格 = ui_page1.输入框_单机抢购价格 or "630"
    配置数据.价格配置.输入框_单机抢购延迟 = ui_page1.输入框_单机抢购延迟 or "30"
    配置数据.价格配置.输入框_资金保护 = ui_page1.输入框_资金保护 or "500000"
    配置数据.价格配置.输入框_房卡输入价格 = ui_page1.输入框_房卡输入价格 or ""
    配置数据.价格配置.输入框_五级蛋价格 = ui_page1.输入框_五级蛋价格 or "150000"
    配置数据.价格配置.输入框_出售价格 = ui_page1.输入框_出售价格 or "758"

    -- 提取时间配置
    配置数据.时间配置.出售_开始时间 = ui_page1.出售_开始时间 or "13"
    配置数据.时间配置.出售_结束时间 = ui_page1.出售_结束时间 or "19"

    return 配置数据
end

-- 将配置应用到UI界面
function 应用配置到UI(界面配置, 配置数据)
    if not 配置数据 or not 界面配置 then
        return false, "参数无效"
    end

    local ui_page0 = 界面配置.page0 or {}
    local ui_page1 = 界面配置.page1 or {}

    -- 应用功能配置
    if 配置数据.功能配置 then
        ui_page0.多选框_选择扫货功能 = 配置数据.功能配置.多选框_选择扫货功能
        ui_page0.多选框_选择房卡功能 = 配置数据.功能配置.多选框_选择房卡功能
        ui_page0.多选框_选择五级蛋 = 配置数据.功能配置.多选框_选择五级蛋
        ui_page0.多选框_出售子弹 = 配置数据.功能配置.多选框_出售子弹
        ui_page0.多选框_单机抢购模式 = 配置数据.功能配置.多选框_单机抢购模式
        ui_page0.扫货_自动挂机 = 配置数据.功能配置.扫货_自动挂机
        ui_page0.特勤处_制作装备 = 配置数据.功能配置.特勤处_制作装备
        ui_page0.子弹类型 = 配置数据.功能配置.子弹类型
    end

    -- 应用价格配置
    if 配置数据.价格配置 then
        ui_page1.输入框_扫货输入价格 = 配置数据.价格配置.输入框_扫货输入价格
        ui_page1.输入框_抢购次数 = 配置数据.价格配置.输入框_抢购次数
        ui_page1.输入框_单机抢购价格 = 配置数据.价格配置.输入框_单机抢购价格
        ui_page1.输入框_单机抢购延迟 = 配置数据.价格配置.输入框_单机抢购延迟
        ui_page1.输入框_资金保护 = 配置数据.价格配置.输入框_资金保护
        ui_page1.输入框_房卡输入价格 = 配置数据.价格配置.输入框_房卡输入价格
        ui_page1.输入框_五级蛋价格 = 配置数据.价格配置.输入框_五级蛋价格
        ui_page1.输入框_出售价格 = 配置数据.价格配置.输入框_出售价格
    end

    -- 应用时间配置
    if 配置数据.时间配置 then
        ui_page1.出售_开始时间 = 配置数据.时间配置.出售_开始时间
        ui_page1.出售_结束时间 = 配置数据.时间配置.出售_结束时间
    end

    return true, "配置应用成功"
end

-- 保存当前UI配置
function 保存当前UI配置(配置名称, 界面配置)
    local 配置数据 = 从UI提取当前配置(界面配置)
    return 配置管理模块:保存配置(配置名称, 配置数据)
end

-- 加载配置并应用到UI
function 加载配置并应用(配置名称, 界面配置)
    local 配置数据, 错误信息 = 配置管理模块:加载配置(配置名称)
    if 配置数据 then
        local 成功, 应用信息 = 应用配置到UI(界面配置, 配置数据)
        if 成功 then
            return true, "配置加载并应用成功"
        else
            return false, "配置应用失败: " .. 应用信息
        end
    else
        return false, "配置加载失败: " .. 错误信息
    end
end

-- 删除指定配置
function 删除指定配置(配置名称)
    return 配置管理模块:删除配置(配置名称)
end

-- 获取配置列表
function 获取所有配置列表()
    return 配置管理模块:获取配置列表()
end

-- =================== 配置管理模块 ===================
-- 配置管理核心模块
local 配置管理模块 = {
    配置目录 = nil,
    配置文件扩展名 = ".json",

    -- 初始化配置目录
    初始化目录 = function(self)
        if not self.配置目录 then
            -- 使用用户可访问的下载目录
            self.配置目录 = "/storage/emulated/0/Download/三角洲行动定制/configs/"
            makeDir(self.配置目录)
            print("🔍 调试：配置目录初始化为: " .. self.配置目录)
        end
        return self.配置目录
    end,

    -- 获取配置文件完整路径
    获取配置路径 = function(self, 配置名称)
        local 目录 = self:初始化目录()
        return 目录 .. 配置名称 .. self.配置文件扩展名
    end,

    -- 获取默认配置结构
    获取默认配置 = function(self)
        return {
            -- 配置元信息
            配置信息 = {
                名称 = "",
                创建时间 = os.time(),
                修改时间 = os.time(),
                版本 = "1.0",
                描述 = ""
            },

            -- 功能选择配置
            功能配置 = {
                多选框_选择扫货功能 = false,
                多选框_选择房卡功能 = false,
                多选框_选择五级蛋 = false,
                多选框_出售子弹 = false,
                多选框_单机抢购模式 = false,
                扫货_自动挂机 = false,
                特勤处_制作装备 = false,
                子弹类型 = 0
            },

            -- 价格配置
            价格配置 = {
                输入框_扫货输入价格 = "630",
                输入框_抢购次数 = "1",
                输入框_单机抢购价格 = "630",
                输入框_单机抢购延迟 = "30",
                输入框_资金保护 = "500000",
                输入框_房卡输入价格 = "",
                输入框_五级蛋价格 = "150000",
                输入框_出售价格 = "758"
            },

            -- 时间配置
            时间配置 = {
                出售_开始时间 = "13",
                出售_结束时间 = "19"
            }
        }
    end,

    -- 保存配置到文件
    保存配置 = function(self, 配置名称, 配置数据)
        if not 配置名称 or 配置名称 == "" then
            return false, "配置名称不能为空"
        end

        -- 清理配置名称，移除非法字符
        配置名称 = string.gsub(配置名称, "[<>:\"/\\|?*]", "_")

        local 文件路径 = self:获取配置路径(配置名称)

        -- 更新配置信息
        if not 配置数据.配置信息 then
            配置数据.配置信息 = {}
        end
        配置数据.配置信息.名称 = 配置名称
        配置数据.配置信息.修改时间 = os.time()
        if not 配置数据.配置信息.创建时间 then
            配置数据.配置信息.创建时间 = os.time()
        end

        local file = io.open(文件路径, "w")
        if file then
            local ok, json_str = pcall(jsonLib.encode, 配置数据)
            if ok then
                file:write(json_str)
                file:close()
                return true, "配置保存成功"
            else
                file:close()
                return false, "配置序列化失败: " .. tostring(json_str)
            end
        else
            return false, "无法创建配置文件: " .. 文件路径
        end
    end,

    -- 从文件加载配置
    加载配置 = function(self, 配置名称)
        if not 配置名称 or 配置名称 == "" then
            return nil, "配置名称不能为空"
        end

        local 文件路径 = self:获取配置路径(配置名称)
        local file = io.open(文件路径, "r")
        if file then
            local content = file:read("*all")
            file:close()

            if content and content ~= "" then
                local ok, 配置数据 = pcall(jsonLib.decode, content)
                if ok and type(配置数据) == "table" then
                    return 配置数据, "配置加载成功"
                else
                    return nil, "配置文件格式错误"
                end
            else
                return nil, "配置文件为空"
            end
        else
            return nil, "配置文件不存在: " .. 配置名称
        end
    end,

    -- 删除配置文件
    删除配置 = function(self, 配置名称)
        if not 配置名称 or 配置名称 == "" then
            return false, "配置名称不能为空"
        end

        local 文件路径 = self:获取配置路径(配置名称)
        local file = io.open(文件路径, "r")
        if file then
            file:close()
            -- 尝试删除文件
            local 删除命令 = string.format('del "%s"', 文件路径)
            local 结果 = os.execute(删除命令)
            if 结果 == 0 then
                return true, "配置删除成功"
            else
                return false, "配置删除失败"
            end
        else
            return false, "配置文件不存在"
        end
    end,

    -- 获取所有配置文件列表
    获取配置列表 = function(self)
        local 目录 = self:初始化目录()
        local 配置列表 = {}

        -- 调试：打印目录信息
        print("🔍 调试：正在扫描配置目录: " .. 目录)

        -- 使用ls命令获取文件列表（适用于Android）
        local 命令 = string.format('ls "%s"*.json 2>/dev/null', 目录)
        print("🔍 调试：执行命令: " .. 命令)

        local 句柄 = io.popen(命令)
        if 句柄 then
            local 文件计数 = 0
            for 文件路径 in 句柄:lines() do
                文件计数 = 文件计数 + 1
                print("🔍 调试：找到文件: " .. 文件路径)

                -- 提取文件名并移除扩展名
                local 文件名 = string.match(文件路径, "([^/]+)%.json$")
                if 文件名 then
                    print("🔍 调试：提取配置名: " .. 文件名)
                    table.insert(配置列表, 文件名)
                else
                    print("🔍 调试：无法提取配置名，文件路径: " .. 文件路径)
                end
            end
            句柄:close()
            print("🔍 调试：ls命令找到 " .. 文件计数 .. " 个文件")
        else
            print("🔍 调试：ls命令失败，使用备用方案")
            -- 如果ls命令失败，尝试直接遍历目录
            local 测试配置名列表 = {"默认配置", "测试配置", "123", "ces", "扫货配置", "房卡配置"}
            for _, 配置名 in ipairs(测试配置名列表) do
                local 文件路径 = self:获取配置路径(配置名)
                print("🔍 调试：检查文件: " .. 文件路径)
                local file = io.open(文件路径, "r")
                if file then
                    file:close()
                    print("🔍 调试：找到配置文件: " .. 配置名)
                    table.insert(配置列表, 配置名)
                else
                    print("🔍 调试：配置文件不存在: " .. 配置名)
                end
            end
        end

        print("🔍 调试：最终配置列表长度: " .. #配置列表)
        return 配置列表
    end
}

-- 从UI界面提取当前配置
function 从UI提取当前配置(界面配置)
    local ui_page0 = 界面配置.page0 or {}
    local ui_page1 = 界面配置.page1 or {}

    local 配置数据 = 配置管理模块:获取默认配置()

    -- 提取功能配置
    配置数据.功能配置.多选框_选择扫货功能 = ui_page0.多选框_选择扫货功能 or false
    配置数据.功能配置.多选框_选择房卡功能 = ui_page0.多选框_选择房卡功能 or false
    配置数据.功能配置.多选框_选择五级蛋 = ui_page0.多选框_选择五级蛋 or false
    配置数据.功能配置.多选框_出售子弹 = ui_page0.多选框_出售子弹 or false
    配置数据.功能配置.多选框_单机抢购模式 = ui_page0.多选框_单机抢购模式 or false
    配置数据.功能配置.扫货_自动挂机 = ui_page0.扫货_自动挂机 or false
    配置数据.功能配置.特勤处_制作装备 = ui_page0.特勤处_制作装备 or false
    配置数据.功能配置.子弹类型 = ui_page0.子弹类型 or 0

    -- 提取价格配置
    配置数据.价格配置.输入框_扫货输入价格 = ui_page1.输入框_扫货输入价格 or "630"
    配置数据.价格配置.输入框_抢购次数 = ui_page1.输入框_抢购次数 or "1"
    配置数据.价格配置.输入框_单机抢购价格 = ui_page1.输入框_单机抢购价格 or "630"
    配置数据.价格配置.输入框_单机抢购延迟 = ui_page1.输入框_单机抢购延迟 or "30"
    配置数据.价格配置.输入框_资金保护 = ui_page1.输入框_资金保护 or "500000"
    配置数据.价格配置.输入框_房卡输入价格 = ui_page1.输入框_房卡输入价格 or ""
    配置数据.价格配置.输入框_五级蛋价格 = ui_page1.输入框_五级蛋价格 or "150000"
    配置数据.价格配置.输入框_出售价格 = ui_page1.输入框_出售价格 or "758"

    -- 提取时间配置
    配置数据.时间配置.出售_开始时间 = ui_page1.出售_开始时间 or "13"
    配置数据.时间配置.出售_结束时间 = ui_page1.出售_结束时间 or "19"

    return 配置数据
end

-- 保存当前UI配置
function 保存当前UI配置(配置名称, 界面配置)
    local 配置数据 = 从UI提取当前配置(界面配置)
    return 配置管理模块:保存配置(配置名称, 配置数据)
end

-- 获取配置列表
function 获取所有配置列表()
    return 配置管理模块:获取配置列表()
end

-- 删除指定配置
function 删除指定配置(配置名称)
    return 配置管理模块:删除配置(配置名称)
end

-- 导出所有函数
return {
    获取当前小时 = 获取当前小时,
    获取当前分钟 = 获取当前分钟,
    在时间段内 = 在时间段内,
    尝试返回大厅 = 尝试返回大厅,
    配置管理模块 = 配置管理模块,
    从UI提取当前配置 = 从UI提取当前配置,
    保存当前UI配置 = 保存当前UI配置,
    获取所有配置列表 = 获取所有配置列表,
    删除指定配置 = 删除指定配置
}