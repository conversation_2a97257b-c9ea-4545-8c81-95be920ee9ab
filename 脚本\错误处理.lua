-- 三角洲行动定制 - 错误处理模块
-- 版本: 1.0.0
-- 功能: 提供统一的错误处理、重试机制和资源清理

-- 统一的错误处理模块
local ErrorHandler = {}

-- 错误类型枚举
ErrorHandler.ErrorType = {
    NETWORK = "NETWORK",
    OCR = "OCR", 
    UI = "UI",
    LOGIC = "LOGIC",
    RESOURCE = "RESOURCE"
}

-- 统一的错误处理函数
function ErrorHandler.safeCall(func, error_type, context, ...)
    local start_time = os.clock()
    local success, result = pcall(func, ...)
    local duration = (os.clock() - start_time) * 1000
    
    if success then
        if Logger then
            Logger.performance(context or "Unknown", "操作成功", duration)
        end
        return true, result
    else
        -- 记录错误
        local error_msg = string.format("[%s] %s: %s", 
                                       error_type or "UNKNOWN", 
                                       context or "Unknown", 
                                       tostring(result))
        
        if Logger then
            Logger.error("ErrorHandler", error_msg, {
                duration_ms = duration,
                error_type = error_type,
                context = context
            })
        else
            print("错误: " .. error_msg)
        end
        
        return false, result
    end
end

-- 重试机制
function ErrorHandler.retryCall(func, max_retries, delay_ms, error_type, context, ...)
    max_retries = max_retries or 3
    delay_ms = delay_ms or 1000
    
    for attempt = 1, max_retries do
        local success, result = ErrorHandler.safeCall(func, error_type, 
                                                     string.format("%s (尝试 %d/%d)", context or "Unknown", attempt, max_retries), 
                                                     ...)
        
        if success then
            return true, result
        end
        
        -- 如果不是最后一次尝试，等待后重试
        if attempt < max_retries then
            if Logger then
                Logger.warn("ErrorHandler", string.format("第 %d 次尝试失败，%d 毫秒后重试", attempt, delay_ms))
            end
            sleep(delay_ms)
        end
    end
    
    return false, "达到最大重试次数"
end

-- 资源清理装饰器
function ErrorHandler.withCleanup(func, cleanup_func, ...)
    local success, result = pcall(func, ...)
    
    -- 无论成功还是失败，都执行清理
    if cleanup_func then
        pcall(cleanup_func)
    end
    
    return success, result
end

return ErrorHandler 