-- 测试配置管理功能的简单脚本
require("工具函数")

print("=== 配置管理功能测试 ===")

-- 获取工具函数模块
local 工具函数模块 = require("工具函数")
if not 工具函数模块 then
    print("❌ 工具函数模块加载失败")
    return
end

print("✅ 工具函数模块加载成功")

-- 检查配置管理模块
local 配置管理模块 = 工具函数模块.配置管理模块
if not 配置管理模块 then
    print("❌ 配置管理模块不存在")
    return
end

print("✅ 配置管理模块存在")

-- 测试创建默认配置
local 默认配置 = 配置管理模块:获取默认配置()
if 默认配置 then
    print("✅ 默认配置创建成功")
    print("  功能配置节存在: " .. tostring(默认配置.功能配置 ~= nil))
    print("  价格配置节存在: " .. tostring(默认配置.价格配置 ~= nil))
    print("  时间配置节存在: " .. tostring(默认配置.时间配置 ~= nil))
else
    print("❌ 默认配置创建失败")
    return
end

-- 测试保存配置
默认配置.配置信息.名称 = "测试配置"
默认配置.功能配置.多选框_选择扫货功能 = true
默认配置.价格配置.输入框_扫货输入价格 = "700"

local 保存成功, 保存信息 = 配置管理模块:保存配置("测试配置", 默认配置)
print("保存结果: " .. tostring(保存成功) .. " - " .. tostring(保存信息))

if 保存成功 then
    print("✅ 配置保存测试通过")
    
    -- 测试加载配置
    local 加载配置, 加载信息 = 配置管理模块:加载配置("测试配置")
    if 加载配置 then
        print("✅ 配置加载测试通过")
        print("  配置名称: " .. tostring(加载配置.配置信息.名称))
        print("  扫货功能: " .. tostring(加载配置.功能配置.多选框_选择扫货功能))
        print("  扫货价格: " .. tostring(加载配置.价格配置.输入框_扫货输入价格))
    else
        print("❌ 配置加载失败: " .. tostring(加载信息))
    end
    
    -- 测试获取配置列表
    local 配置列表 = 配置管理模块:获取配置列表()
    print("✅ 配置列表获取成功，共 " .. #配置列表 .. " 个配置")
    for i, 配置名 in ipairs(配置列表) do
        print("  " .. i .. ". " .. 配置名)
    end
    
    -- 清理测试配置
    local 删除成功, 删除信息 = 配置管理模块:删除配置("测试配置")
    print("删除结果: " .. tostring(删除成功) .. " - " .. tostring(删除信息))
    
else
    print("❌ 配置保存测试失败")
end

-- 测试全局函数
print("\n=== 测试全局函数 ===")
local 全局函数列表 = {
    "从UI提取当前配置",
    "保存当前UI配置", 
    "获取所有配置列表",
    "删除指定配置"
}

for _, 函数名 in ipairs(全局函数列表) do
    if 工具函数模块[函数名] then
        print("✅ " .. 函数名 .. " 存在")
    else
        print("❌ " .. 函数名 .. " 不存在")
    end
end

print("\n=== 测试完成 ===")
