--[[
模块：自动出售子弹 (重构版 - 修复全自动模式交易行检测)
功能：封装所有与自动出售子弹相关的功能，使用通用、健壮的逻辑。
优化：根据代码审查建议，将坐标配置化，并重构循环与错误处理机制。
修复：解决全自动模式下交易行重复检测导致的卡死问题。
更新：修改价格设置流程为：输入价格→延迟1秒→点击确认→OCR验证
]]

-- 导入依赖
local OCR功能 = require("OCR功能")
local 工具函数 = require("工具函数")
-- 显式导入模块，避免依赖全局变量
local 自动领取邮件 = require("自动领取邮件")

local M = {}

-- 定义统一的返回状态码，增强代码可读性和可维护性
local 状态码 = {
	成功 = "SUCCESS" ,
	失败 = "FAILED" ,
	售罄 = "SOLD_OUT"
}

-- =================== 模块配置中心 ===================
M.配置 = {
	子弹 = {
		["9_19"] = {
			名称 = "9_19子弹" ,
			图片 = "雷电云_9_19_2.png|9_19.png|9_19_1.png|9_19_2.png|雷电云_9_19.png|雷电云_9_19_1.png" ,
			容差 = "101010" ,
			相似度 = 0.9
		} ,
		["57_28"] = {
			名称 = "5.7子弹" ,
			图片 = "雷电云_出售_5.7_3.png|出售_5.7.png|出售_5.7_4.png|出售_5.7_2.png|出售_5.7_3.png|雷电云_出售_5.7.png|雷电云_出售_5.7_2.png" ,
			容差 = "101010" ,
			相似度 = 0.8
		} ,
		["45ACP"] = {
			名称 = ".45ACP子弹" ,
			图片 = "出售_45ACP.png|出售_45ACP_1.png|出售_45ACP_2.png|出售_45ACP_3.png" ,
			容差 = "202020" ,
			相似度 = 0.8
		} ,
		["12_Gauge"] = {
			名称 = "12号口径霰弹" ,
			图片 = "12_Gauge_肉蛋.png|12_Gauge_AP20.png|12_Gauge_箭形弹.png|12_Gauge_龙息弹.png|12_Gauge_FTX.png" ,
			容差 = "151515" ,
			相似度 = 0.8
		}
	} ,
	区域 = {
		交易行按钮 = {284 , 652 , 376 , 683} ,
		出售按钮 = {12,145,122,234} ,
		整理按钮 = {1039 , 642 , 1279 , 719} ,
		确认整理按钮 = {1039 , 642 , 1279 , 719} ,
		子弹查找 = {565 , 93 , 1215 , 381} ,
		选择数量 = {1138 , 364 , 1219 , 423} ,
		价格输入框 = {896 , 453 , 1106 , 493} ,
		价格确认按钮 = {1000 , 159 , 1279 , 719} ,
		打开价格输入 = {850,449,1132,499} ,
		上架按钮 = {831 , 538 , 1151 , 615} ,
		数量进度条 = {791 , 352 , 1204 , 431} ,
		卡顿关闭 = {1152 , 29 , 1274 , 132} ,
		查看是否出售 = {127,240,548,414} ,
		等待上架 = {122 , 91 , 530 , 238} ,
		翻页 = {1216 , 58 , 1279 , 719} ,
		返回大厅 = {0 , 0 , 163 , 53}
	} ,
	颜色 = {
		交易行按钮 = {"eaebeb" , "-12|13|e7e8e8|-34|6|d6d7d7"} ,
		打开价格输入 = {"818487","1|4|e2e3e3|19|1|808486"} ,
		上架按钮 = {"10cb7e" , "5|-8|10cb7e|-8|-8|11be77|-15|-8|11b975"} ,
		翻页按钮 = {"ffffff-202020" , ""}
	} ,
	图片 = {
		出售按钮 = "出售_点击出售按钮.png|出售_点击出售按钮1.png" ,
		整理按钮 = "出售_点击整理.png|出售_点击整理1.png" ,
		确认整理按钮 = "出售_确认整理.png|出售_确认整理1.png" ,
		选择数量 = "出售_选择数量.png|出售_选择数量1.png" ,
		价格确认按钮 = "出售_确认价格.png|出售_确认价格1.png" ,
		数量进度条 = "出售_选择进度条.png" ,
		卡顿关闭 = "出售_卡顿关闭.png" ,
		查看是否出售 = "出售_识别查看是否出售.png|出售_识别查看是否出售1.png" ,
		等待上架 = "自动出售_等待上架.png|自动出售_等待上架.png" ,
		返回大厅 = "领取邮件_交易行返回大厅.png|领取邮件_交易行返回大厅1.png"
	} ,
	参数 = {
		最大翻页次数 = 7 ,
		最大上架尝试次数 = 25 ,
		最大卡顿处理次数 = 3 ,
		最大交易行查找重试次数 = 5 ,
		出售成功次数领取邮件 = 3 ,
		价格设置重试次数 = 5
	}
}

-- =================== 内部工具函数 (优化版) ===================

-- 【用户要求#2】修复 安全领取邮件 函数
-- 【最终修复】根据您的反馈，修正语法错误
local function 安全领取邮件()
	-- 检查是否有全局的自动领取邮件函数
	if _G.自动领取邮件 and type(_G.自动领取邮件) == "function" then
		local ok , result = pcall(_G.自动领取邮件)
		if ok then
			显示信息("✅ 邮件领取完成")
		else
			显示信息("⚠️ 邮件领取过程中出现问题：" .. tostring(result))
		end
		return
	end
	
	-- 尝试动态加载邮件模块
	local mail_ok , mail_module = pcall(require , "自动领取邮件")
	if mail_ok then
		if type(mail_module) == "function" then
			local ok , result = pcall(mail_module)
			if ok then
				显示信息("✅ 邮件领取完成")
			else
				显示信息("⚠️ 邮件领取失败：" .. tostring(result))
			end
		elseif type(mail_module) == "table" and mail_module.main then
			local ok , result = pcall(mail_module.main)
			if ok then
				显示信息("✅ 邮件领取完成")
			else
				显示信息("⚠️ 邮件领取失败：" .. tostring(result))
			end
		else
			显示信息("⚠️ 自动领取邮件模块格式不正确")
		end
	else
		显示信息("⚠️ 自动领取邮件模块加载失败：" .. tostring(mail_module))
	end
end

local function 显示信息(msg , 显示Toast)
	local 时间戳 = os.date("%Y-%m-%d %H:%M:%S")
	print(string.format("[%s] %s" , 时间戳 , msg))
	
	-- 只在重要信息时显示toast
	if 显示Toast ~= false and (
		string.find(msg , "错误") or
		string.find(msg , "失败") or
		string.find(msg , "✅") or
		string.find(msg , "❌") or
		string.find(msg , "完成") or
		string.find(msg , "启动") or
		string.find(msg , "成功")
		) then
		toast(msg)
	end
	
	-- 记录到日志文件
	if Logger then
		if string.find(msg , "错误") or string.find(msg , "失败") or string.find(msg , "❌") then
			Logger.error("出售子弹" , msg)
		elseif string.find(msg , "警告") or string.find(msg , "⚠️") then
			Logger.warn("出售子弹" , msg)
		else
			Logger.info("出售子弹" , msg)
		end
	end
end

local function 等待界面元素出现(区域 , 图片 , 等待时间 , 描述)
	local 开始时间 = os.time()
	local 超时时间 = 等待时间 or 3
	
	while (os.time() - 开始时间) < 超时时间 do
		local _ , x , y = findPic(区域[1] , 区域[2] , 区域[3] , 区域[4] , 图片 , "101010" , 0 , 0.7)
		if x > - 1 and y > - 1 then
			显示信息(string.format("✓ 成功等待到 %s" , 描述))
			return true , x , y
		end
		sleep(300)
	end
	显示信息(string.format("✗ 等待 %s 超时" , 描述))
	return false , - 1 , - 1
end

-- 【修复全自动模式BUG】增强的交易行状态检测
local function 检查是否已在交易行()
	-- 只使用最可靠的方法：检查是否存在出售按钮
	local R_sell = M.配置.区域.出售按钮
	local Img_sell = M.配置.图片.出售按钮
	local _ , x , y = findPic(R_sell[1] , R_sell[2] , R_sell[3] , R_sell[4] , Img_sell , "101010" , 0 , 0.7)
	if x > - 1 then
		显示信息("✓ 检测到出售按钮，已在交易行")
		return true
	end
	
	显示信息("✗ 未检测到交易行界面")
	return false
end

-- 【修改】增强OCR价格验证的健壮性 - 用于确认后的验证
local function 验证输入框价格(期望价格)
	local R = M.配置.区域.价格输入框
	显示信息("→ 开始价格验证（OCR识别）...")
	
	local ok , price_text = pcall(OCR功能.ocr_start , R[1] , R[2] , R[3] , R[4])
	if not ok or not price_text then
		显示信息("✗ OCR识别失败: " .. tostring(price_text))
		return false
	end
	
	-- 清理OCR结果中的常见非数字字符，提高识别准确率
	local cleaned_price_text = string.gsub(tostring(price_text) , "[^%d%.]" , "")
	local price_num = tonumber(cleaned_price_text)
	local expected_num = tonumber(期望价格)
	
	if price_num and expected_num and price_num == expected_num then
		显示信息(string.format("✓ 价格验证成功: OCR识别到 %s，与期望价格匹配。" , tostring(price_num)))
		return true
	else
		-- 提供更详细的调试信息
		显示信息(string.format("✗ 价格验证失败: OCR原始值='%s', 清理后='%s' (数值: %s), 期望值='%s'" ,
		tostring(price_text) , cleaned_price_text , tostring(price_num) , tostring(期望价格)))
		return false
	end
end

-- 【重要修改】按要求修改价格设置流程：输入→延迟1秒→确认→OCR验证 (重构版)
local function 设置出售价格(期望价格)
	local 重试次数 = 0
	local 最大重试次数 = M.配置.参数.价格设置重试次数
	local R_confirm = M.配置.区域.价格确认按钮
	local Img_confirm = M.配置.图片.价格确认按钮
	
	while 重试次数 < 最大重试次数 do
		重试次数 = 重试次数 + 1
		显示信息(string.format("第 %d/%d 次尝试设置价格..." , 重试次数 , 最大重试次数))
		
		-- 步骤1：在每次循环开始时，都检查价格输入界面是否打开
		local _ , kx , ky = findPic(R_confirm[1] , R_confirm[2] , R_confirm[3] , R_confirm[4] , Img_confirm , "101010" , 0 , 0.7)
		if kx == - 1 then
			显示信息("价格输入界面未打开，尝试点击打开...")
			local R_open = M.配置.区域.打开价格输入
			local C_open = M.配置.颜色.打开价格输入
			local x_ref , y_ref = findMultiColor(R_open[1] , R_open[2] , R_open[3] , R_open[4] , C_open[1] , C_open[2] , 0 , 0.7)
			if x_ref > - 1 then
				显示信息(string.format("✓ 找到打开按钮，点击坐标: (%d, %d)" , x_ref , y_ref))
				tap(x_ref , y_ref+15)
				sleep(2000) -- 增加等待时间确保界面弹出
			else
				显示信息("✗ 未找到用于打开价格输入界面的按钮，本次尝试失败。")
				sleep(1000)
				goto continue_loop -- 跳转到下一次循环
			end
		end
		
		-- 步骤2：清空、输入并确认价格
		-- 清空输入框
		for i = 1 , 12 do
			imeLib.deleteChar()
			sleep(50)
		end
		
		-- 输入价格
		inputText(tostring(期望价格))
		显示信息(string.format("已输入价格: %s" , tostring(期望价格)))
		
		-- 【关键修改】按要求延迟1秒
		sleep(1000)
		
		-- 【关键修改】先点击确认按钮
		显示信息("延迟1秒后，准备点击确认按钮...")
		local 找到确认 = _G.查找并点击(R_confirm[1] , R_confirm[2] , R_confirm[3] , R_confirm[4] , Img_confirm , "101010" , 0.7 , 1500 , "价格确认按钮")
		
		if 找到确认 then
			显示信息("✓ 已点击确认按钮，开始OCR验证...")
		else
			-- 【修改】未找到确认按钮时，直接继续执行OCR验证
			显示信息("✓ 未找到确认价格按钮，直接进行OCR验证...")
		end
		
		-- 【关键修改】无论是否找到确认按钮，都进行OCR验证
		sleep(500) -- 等待界面稍微稳定后再OCR
		if 验证输入框价格(期望价格) then
			显示信息("✓ 价格设置并确认成功！")
			return true
		else
			显示信息("✗ OCR验证失败，将重试...")
		end
		
		::continue_loop:: -- goto 跳转标签
	end
	
	显示信息("⚠️ 价格设置失败，已达最大重试次数。")
	return false
end

-- =================== 核心步骤函数 ===================

-- 【修改】步骤_打开交易行函数，增加实际点击交易行的逻辑
local function 步骤_打开交易行(状态)
	显示信息("[步骤 1/5] 正在强制打开交易行...")
	
	--[[
	-- 根据用户反馈，移除交易行预检查，强制执行返回大厅并打开的流程，确保步骤的可靠性。
	if 检查是否已在交易行() then
		显示信息("✓ 已在交易行，跳过打开步骤")
		return true
	end
	--]]
	
	-- 强制返回大厅
	if 工具函数 and 工具函数.尝试返回大厅 then
		工具函数.尝试返回大厅("准备打开交易行")
		sleep(1500)
	end
	
	-- 在大厅中查找并点击交易行按钮
	local R_trade = M.配置.区域.交易行按钮
	local C_trade = M.配置.颜色.交易行按钮
	local x , y = findMultiColor(R_trade[1] , R_trade[2] , R_trade[3] , R_trade[4] , C_trade[1] , C_trade[2] , 0 , 0.7)
	if x > - 1 then
		显示信息("找到交易行按钮，点击打开...")
		tap(x , y)
		sleep(3000) -- 等待交易行界面加载
		
		-- 验证是否成功进入交易行
		local max_attempts = 3
		for i = 1 , max_attempts do
			if 检查是否已在交易行() then
				显示信息("✓ 成功进入交易行")
				return true
			end
			显示信息(string.format("第%d次等待交易行加载..." , i))
			sleep(1000)
		end
	end
	
	显示信息("✗ 未能成功打开交易行")
	return false
end

local function 步骤_进入出售并整理()
	显示信息("[步骤 2/5] 正在进入出售界面...")
	local 找到出售 , x , y = 等待界面元素出现(M.配置.区域.出售按钮 , M.配置.图片.出售按钮 , 3 , "出售按钮")
	if not 找到出售 then
		显示信息("✗ 未找到出售按钮，可能不在交易行界面")
		return false
	end
	tap(x , y)
	sleep(1500)
	
	-- 【修复】将整理步骤作为必要验证，确保界面已正确加载
	显示信息("[步骤 3/5] 正在整理物品...")
	local 找到整理 , tx , ty = 等待界面元素出现(M.配置.区域.整理按钮 , M.配置.图片.整理按钮 , 3 , "整理按钮")
	if not 找到整理 then
		显示信息("✗ 未找到'整理'按钮，界面可能异常，将返回大厅重置。")
		return false -- 关键修复：如果找不到整理按钮，则认为此步骤失败
	end
	
	-- 既然找到了，就点击它
	tap(tx , ty)
	sleep(1000)
	
	-- 确认整理按钮仍然是可选的
	显示信息("[步骤 4/5] 正在确认整理 (可选)...")
	local 找到确认 , cx , cy = 等待界面元素出现(M.配置.区域.确认整理按钮 , M.配置.图片.确认整理按钮 , 2 , "确认整理按钮")
	if 找到确认 then
		tap(cx , cy)
		sleep(1500)
	else
		显示信息("✓ 未找到'确认整理'按钮，跳过。")
	end
	
	-- 因为整理是可选的，所以只要进入出售界面就算成功
	return true
end

-- 步骤函数返回统一的状态表
local function 步骤_查找并上架子弹(配置 , 售价 , 状态)
	显示信息("[步骤 5/5] 开始查找 " .. 配置.名称 .. "...")
	local R_find = M.配置.区域.子弹查找
	local R_page = M.配置.区域.翻页
	local C_page = M.配置.颜色.翻页按钮
	
	for i = 1 , M.配置.参数.最大翻页次数 do
		local _ , x , y = findPic(R_find[1] , R_find[2] , R_find[3] , R_find[4] , 配置.图片 , 配置.容差 , 0 , 配置.相似度)
		if x > - 1 then
			显示信息("✓ 找到子弹箱，准备出售...")
			tap(x , y)
			sleep(2000)
			
			-- 增强：使用用户建议的精确找图来选择数量
			local success , x , y = 找图_区域找图(1148 , 362 , 1206 , 426 , "出售_选择数量.png" , "151515" , 0.9 , 0 , false)
			if success then
				tap(x - 37 , y + 6)
				显示信息("✓ 找到数量选择按钮，已点击最大化。")
				sleep(1500)
			else
				显示信息("✗ 未找到最大化数量按钮，将按默认数量出售。")
			end
			
			if not 设置出售价格(售价) then
				return { 状态 = 状态码.失败 , 消息 = "设置出售价格失败" }
			end
			
			local 上架成功 = 步骤_智能上架()
			if not 上架成功 then
				return { 状态 = 状态码.失败 , 消息 = "智能上架步骤失败" }
			end
			
			步骤_等待出售(状态)
			return { 状态 = 状态码.成功 , 消息 = "上架成功并等待出售" }
		else
			显示信息(string.format("✗ 未找到子弹箱，翻页继续查找（%d/%d）" , i , M.配置.参数.最大翻页次数))
			local fx , fy = findMultiColor(R_page[1] , R_page[2] , R_page[3] , R_page[4] , C_page[1] , C_page[2] , 0 , 0.7)
			if fx > - 1 then tap(fx + 3 , fy + 85) sleep(1000) else 显示信息("✗ 未找到翻页按钮") sleep(1000) end
		end
	end
	
	显示信息("==== " .. 配置.名称 .. " 已售完，本轮结束 ====")
	if _G.State then
		_G.State.所有子弹已售罄 = true
		显示信息("【修正】已设置State.所有子弹已售罄=true")
	end
	return { 状态 = 状态码.售罄 , 消息 = 配置.名称 .. " 已售罄" }
end

function 步骤_智能上架()
	显示信息("→ 准备上架...")
	local R = M.配置.区域.上架按钮
	local C = M.配置.颜色.上架按钮
	local attempt_count , 卡顿处理次数 = 0 , 0
	
	while attempt_count < M.配置.参数.最大上架尝试次数 do
		attempt_count = attempt_count + 1
		
		local lx , ly = findMultiColor(R[1] , R[2] , R[3] , R[4] , C[1] , C[2] , 0 , 0.9)
		if lx > - 1 then
			显示信息("✓ 发现可点击的上架按钮")
			tap(lx , ly)
			sleep(1000)
			return true
		end
		
		显示信息("上架按钮不可点击，需要调整数量...")
		local R_bar = M.配置.区域.数量进度条
		local Img_bar = M.配置.图片.数量进度条
		local _ , bar_x , bar_y = findPic(R_bar[1] , R_bar[2] , R_bar[3] , R_bar[4] , Img_bar , "101010" , 0 , 0.7)
		if bar_x > - 1 then
			显示信息("调整数量中...")
			tap(bar_x - 12 , bar_y + 11)
			sleep(200)
		end
		
		if attempt_count >= M.配置.参数.最大上架尝试次数 and 卡顿处理次数 < M.配置.参数.最大卡顿处理次数 then
			显示信息(string.format("检查卡顿 (%d/%d)..." , 卡顿处理次数 + 1 , M.配置.参数.最大卡顿处理次数))
			local R_stuck = M.配置.区域.卡顿关闭
			local Img_stuck = M.配置.图片.卡顿关闭
			local _ , close_x , close_y = findPic(R_stuck[1] , R_stuck[2] , R_stuck[3] , R_stuck[4] , Img_stuck , "101010" , 0 , 0.7)
			if close_x > - 1 then
				tap(close_x , close_y)
				显示信息("✓ 处理了卡顿")
				卡顿处理次数 = 卡顿处理次数 + 1
				sleep(1500)
				attempt_count = 0 -- 重置尝试次数
			end
		end
		sleep(800)
	end
	
	显示信息("⚠ 上架失败，达到最大重试次数")
	return false
end

function 步骤_等待出售(状态)
	while true do
		local R_sold = M.配置.区域.查看是否出售
		local Img_sold = M.配置.图片.查看是否出售
		local _ , still_x , _ = findPic(R_sold[1] , R_sold[2] , R_sold[3] , R_sold[4] , Img_sold , "101010" , 0 , 0.7)
		
		if still_x == - 1 then
			状态.成功出售次数 = 状态.成功出售次数 + 1
			显示信息(string.format("✓ 成功出售！进度：%d/%d" , 状态.成功出售次数 , M.配置.参数.出售成功次数领取邮件))
			if 状态.成功出售次数 >= M.配置.参数.出售成功次数领取邮件 then
				显示信息("已达到出售次数，准备领取邮件...")
				安全领取邮件() -- 使用封装好的安全函数
				状态.成功出售次数 = 0
				显示信息("邮件领取完成，重置计数")
			end
			return -- 结束等待
		end
		
		local R_wait = M.配置.区域.等待上架
		local Img_wait = M.配置.图片.等待上架
		local _ , wait_x , _ = findPic(R_wait[1] , R_wait[2] , R_wait[3] , R_wait[4] , Img_wait , "101010" , 0 , 0.7)
		if wait_x > - 1 then
			显示信息("检测到等待上架状态，重新开始本轮...")
			return -- 结束等待，让主循环重来
		end
		
		显示信息("→ 等待买家中...")
		sleep(3000)
	end
end

-- =================== 通用出售主函数 (重构版) ===================
local function 通用自动出售(子弹类型名 , 出售价格 , 状态)
	local 配置 = M.配置.子弹[子弹类型名]
	if not 配置 then
		local msg = "错误：未知的子弹类型：" .. tostring(子弹类型名)
		显示信息(msg)
		return { 状态 = 状态码.失败 , 消息 = msg }
	end
	
	显示信息(string.format("... 开始【%s】一轮出售, 价格: %s ..." , 配置.名称 , tostring(出售价格)))
	
	-- 【修复全自动模式BUG】改为更清晰的顺序逻辑，并增强错误处理
	local 打开交易行成功 = 步骤_打开交易行(状态)
	if not 打开交易行成功 then
		显示信息("本轮出售失败（打开交易行失败），将返回大厅并重试...")
		工具函数.尝试返回大厅("打开交易行失败")
		return { 状态 = 状态码.失败 , 消息 = "打开交易行失败" }
	end
	
	local 进入出售成功 = 步骤_进入出售并整理()
	if not 进入出售成功 then
		显示信息("本轮出售失败（进入出售界面失败），将返回大厅并重试...")
		工具函数.尝试返回大厅("进入出售界面失败")
		return { 状态 = 状态码.失败 , 消息 = "进入出售并整理失败" }
	end
	
	local 上架结果 = 步骤_查找并上架子弹(配置 , 出售价格 , 状态)
	-- 根据上架结果进行后续处理
	if 上架结果.状态 == 状态码.失败 then
		显示信息("本轮出售失败（查找或上架失败），将返回大厅并重试...")
		工具函数.尝试返回大厅("查找或上架子弹失败")
	elseif 上架结果.状态 == 状态码.售罄 then
		-- 此处逻辑已移至主脚本，保持为空
	end
	
	显示信息(string.format("==== 【%s】出售检查结束，状态: %s ====" , 配置.名称 , 上架结果.状态))
	return 上架结果
end

-- =================== 模块导出函数 ===================
-- 增加状态参数的初始化，确保脚本健壮性
local function 导出函数封装(子弹类型名 , 出售价格 , 状态)
	-- 确保状态参数完整性
	状态 = 状态 or {}
	状态.交易行查找重试次数 = 状态.交易行查找重试次数 or 0
	状态.成功出售次数 = 状态.成功出售次数 or 0
	return 通用自动出售(子弹类型名 , 出售价格 , 状态)
end

function M.自动出售9_19(出售价格 , 状态)
	return 导出函数封装("9_19" , 出售价格 , 状态)
end

function M.自动出售57_28(出售价格 , 状态)
	return 导出函数封装("57_28" , 出售价格 , 状态)
end

function M.自动出售45ACP(出售价格 , 状态)
	return 导出函数封装("45ACP" , 出售价格 , 状态)
end

function M.自动出售12_Gauge(出售价格 , 状态)
	return 导出函数封装("12_Gauge" , 出售价格 , 状态)
end

return M

--</rewritten_file>
