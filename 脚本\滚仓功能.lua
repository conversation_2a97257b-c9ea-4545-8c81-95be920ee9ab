-- 导入必要的系统模块
import('java.io.File')
import('java.lang.*')
import('android.content.Context')
import('com.nx.assist.lua.LuaEngine')

-- 使用三角洲定制中的OCR识字参数
local OCR_PARAMS = {
	["扫货_识别价格"] = {
		x1 = 1042 ,
		y1 = 640 ,
		x2 = 1230 ,
		y2 = 680 ,
		color = "101010" ,
		sim = 0.7
	}
}

-- 初始化OCR功能
local loader = LuaEngine.loadApk("/storage/emulated/0/Download/TomatoOCR.apk")
local OCR = loader.loadClass("com.tomato.ocr.lr.OCRApi")
local tmo_ocr = OCR.init(LuaEngine.getContext())
local license = "XLLCXLQMGSQRM6UG7OPHX40YD6E7AO9K|rFkWHeO5hZKEJ4XsJJOi4ZVI"
local flag = tmo_ocr.setLicense(license)

-- 全局变量用于存储滚仓配置
local 滚仓_子弹数量 = 0 -- 抢购子弹数量
local 滚仓_子弹价格 = 0 -- 单个子弹最高价格
local 滚仓_总价格 = 0

-- OCR识别函数
local function ocr_start(x1 , y1 , x2 , y2 , zi)
	-- 设置OCR参数
	tmo_ocr.setRecType("ch-3.0") -- 中文识别模型
	tmo_ocr.setReturnType("num") -- 仅返回数字
	tmo_ocr.setRunMode("fast") -- 快速模式
	tmo_ocr.setRecScoreThreshold(0.8) -- 识别置信度阈值
	
	local type = 2 -- 使用只识别模式
	
	-- 截取屏幕区域为bitmap并识别
	local bitmap = LuaEngine.snapShot(x1 , y1 , x2 , y2)
	local result = tmo_ocr.ocrBitmap(bitmap , type)
	bitmap.recycle()
	
	return result
end

-- 从UI界面读取滚仓配置
local function 读取滚仓配置()
	-- 读取子弹数量
	local 子弹数量文本 = getUIContent("抢购子弹数量")
	滚仓_子弹数量 = tonumber(子弹数量文本) or 0
	
	-- 读取单价
	local 子弹价格文本 = getUIContent("单个子弹价格")
	滚仓_子弹价格 = tonumber(子弹价格文本) or 0
	
	显示信息(string.format("已读取滚仓配置 - 数量:%d, 单价:%d" , 滚仓_子弹数量 , 滚仓_子弹价格))
end

-- 主要的滚仓功能实现
function 滚仓功能()
	显示信息("=== 滚仓功能开始执行 ===", false)
	
	-- 详细的全局变量状态检查
	显示信息(string.format("检查全局变量 - 数量:%s(类型:%s), 价格:%s(类型:%s)", 
		tostring(_G.滚仓_子弹数量), type(_G.滚仓_子弹数量),
		tostring(_G.滚仓_子弹价格), type(_G.滚仓_子弹价格)), false)
	
	-- 🔧 修复：更严格的条件检查 - 确保变量不仅存在，而且是有效的正数
	if _G.滚仓_子弹数量 ~= nil and _G.滚仓_子弹价格 ~= nil 
	   and type(_G.滚仓_子弹数量) == "number" and type(_G.滚仓_子弹价格) == "number"
	   and _G.滚仓_子弹数量 > 0 and _G.滚仓_子弹价格 > 0 then
		   
		滚仓_子弹数量 = _G.滚仓_子弹数量
		滚仓_子弹价格 = _G.滚仓_子弹价格
		滚仓_总价格 = 滚仓_子弹数量 * 滚仓_子弹价格
		
		显示信息(string.format("✅ 从全局变量读取配置 - 数量:%d, 单价:%d, 总价格:%d" ,
			滚仓_子弹数量 , 滚仓_子弹价格 , 滚仓_总价格), false)
	else
		-- 详细的错误信息，帮助诊断问题
		显示信息("❌ 全局配置检查失败，详细信息：", false)
		
		-- 检查每个变量的具体状态
		if _G.滚仓_子弹数量 == nil then
			显示信息("  - _G.滚仓_子弹数量 是 nil", false)
		elseif type(_G.滚仓_子弹数量) ~= "number" then
			显示信息(string.format("  - _G.滚仓_子弹数量 类型错误: %s (值:%s)", 
				type(_G.滚仓_子弹数量), tostring(_G.滚仓_子弹数量)), false)
		elseif _G.滚仓_子弹数量 <= 0 then
			显示信息(string.format("  - _G.滚仓_子弹数量 值无效: %s (应该>0)", 
				tostring(_G.滚仓_子弹数量)), false)
		end
		
		if _G.滚仓_子弹价格 == nil then
			显示信息("  - _G.滚仓_子弹价格 是 nil", false)
		elseif type(_G.滚仓_子弹价格) ~= "number" then
			显示信息(string.format("  - _G.滚仓_子弹价格 类型错误: %s (值:%s)", 
				type(_G.滚仓_子弹价格), tostring(_G.滚仓_子弹价格)), false)
		elseif _G.滚仓_子弹价格 <= 0 then
			显示信息(string.format("  - _G.滚仓_子弹价格 值无效: %s (应该>0)", 
				tostring(_G.滚仓_子弹价格)), false)
		end
		
		显示信息("错误：全局配置无效，无法继续执行滚仓功能", false)
		
		-- 尝试使用本地默认配置（如果有的话）
		if 滚仓_子弹价格 > 0 and 滚仓_子弹数量 > 0 then
			显示信息(string.format("尝试使用模块内部配置 - 数量:%d, 价格:%d", 
				滚仓_子弹数量, 滚仓_子弹价格), false)
			滚仓_总价格 = 滚仓_子弹价格 * 滚仓_子弹数量
		else
			显示信息("模块内部配置也无效，退出执行", false)
			return
		end
	end
	
	-- 最终验证配置
	if not 滚仓_总价格 or 滚仓_总价格 <= 0 then
		显示信息(string.format("错误：计算出的总价格无效 (%s)，请检查配置" , tostring(滚仓_总价格)), false)
		return
	end
	
	显示信息(string.format("✅ 配置验证通过，开始滚仓循环 - 数量:%d, 单价:%d, 总价:%d", 
		滚仓_子弹数量, 滚仓_子弹价格, 滚仓_总价格), false)

	local 循环次数 = 0
	while true do
		循环次数 = 循环次数 + 1
		--显示信息(string.format("=== 开始第 %d 轮滚仓检查 ===", 循环次数), false)
		
		显示信息("开始执行方案一检查...", false)
		local 找到 , x , y = 找图_区域找图(5 , 161 , 253 , 242 , "滚仓_点击自定义方案一.png" , "101010" , 0.9 , 0 , true)
		if 找到 then
			显示信息("找到方案一按钮，准备检查价格", false)
		end
		
		local 滚仓价格文本 = ocr_start(1031 , 637 , 1236 , 683)
		local 滚仓价格 = tonumber(滚仓价格文本)
		显示信息(string.format("方案一当前价格：%s", tostring(滚仓价格)), false)
		
		-- 🔧 修复：添加 nil 检查，防止比较错误
		if 滚仓价格 and 滚仓价格 < 滚仓_总价格 then
			显示信息("方案一价格合适，尝试购买", false)
			local 找到 , x , y = 找色_区域多点找色(963 , 608 , 1279 , 702 , "0d4e38" , "-3|3|0eca7d|-5|-11|0eca7d" , 0 , 0.9 , true)
			if 找到 then
				显示信息("方案一购买操作完成", false)
			end
		else
			if 滚仓价格 then
				显示信息(string.format("方案一价格过高，跳过购买 (当前:%d > 限制:%d)", 滚仓价格, 滚仓_总价格), false)
			else
				显示信息("方案一价格识别失败，跳过购买", false)
			end
		end
		
		显示信息("开始执行方案二检查...", false)
		local 找到 , x , y = 找图_区域找图(9 , 234 , 254 , 305 , "滚仓_点击自定义方案二.png" , "101010" , 0.9 , 0 , true)
		if 找到 then
			显示信息("找到方案二按钮，准备检查价格", false)
		end
		
		local 滚仓价格文本 = ocr_start(1031 , 637 , 1236 , 683)
		local 滚仓价格 = tonumber(滚仓价格文本)
		显示信息(string.format("方案二当前价格：%s", tostring(滚仓价格)), false)
		
		-- 🔧 修复：添加 nil 检查，防止比较错误
		if 滚仓价格 and 滚仓价格 < 滚仓_总价格 then
			显示信息("方案二价格合适，尝试购买", false)
			local 找到 , x , y = 找色_区域多点找色(963 , 608 , 1279 , 702 , "0d4e38" , "-3|3|0eca7d|-5|-11|0eca7d" , 0 , 0.9 , true)
			if 找到 then
				显示信息("方案二购买操作完成", false)
			end
		else
			if 滚仓价格 then
				显示信息(string.format("方案二价格过高，跳过购买 (当前:%d > 限制:%d)", 滚仓价格, 滚仓_总价格), false)
			else
				显示信息("方案二价格识别失败，跳过购买", false)
			end
		end
		
	end
end

-- 导出模块
local M = {
	读取滚仓配置 = 读取滚仓配置 ,
	ocr_start = ocr_start ,
	滚仓功能 = 滚仓功能 , -- 导出主功能
	-- 导出配置变量的访问器
	get滚仓_子弹数量 = function() return 滚仓_子弹数量 end ,
	get滚仓_子弹价格 = function() return 滚仓_子弹价格 end
}

return M
