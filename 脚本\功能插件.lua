-- ==================== 全局配置管理 ====================

-- 日志级别定义
local 日志级别 = {
    调试 = 1,
    信息 = 2,
    警告 = 3,
    错误 = 4,
    致命 = 5
}

local 全局配置 = {
    默认超时时间 = 30,
    默认重试次数 = 3,
    默认相似度 = 0.9,
    默认偏色值 = "000000",
    默认查找方向 = 0,
    调试模式 = false,
    错误日志 = true,
    性能监控 = false,
    
    -- 日志相关配置
    启用日志文件 = false,        -- 是否启用日志文件记录
    日志文件路径 = "/storage/emulated/0/Download/脚本日志.txt",
    最低显示级别 = 日志级别.信息,  -- 最低显示级别
    显示时间戳 = true,          -- 是否显示时间戳
    显示级别标识 = true,        -- 是否显示级别标识
    最大日志文件大小 = 1024 * 1024, -- 1MB
    保留历史日志数量 = 5
}

function 获取配置(键名)
    return 全局配置[键名]
end

function 设置配置(键名, 值)
    if 全局配置[键名] ~= nil then
        全局配置[键名] = 值
        if 全局配置.调试模式 then
            显示信息(string.format("配置已更新：%s = %s", 键名, tostring(值)))
        end
        return true
    else
        -- 这里我们使用新的显示错误函数
        显示错误("设置配置失败: 未知的配置项 " .. 键名)
        return false
    end
end

-- ==================== 增强的日志系统 ====================

-- 日志级别颜色映射（用于不同显示效果）
local 级别颜色 = {
    [1] = "🔍", -- 调试
    [2] = "ℹ️",  -- 信息
    [3] = "⚠️",  -- 警告
    [4] = "❌", -- 错误
    [5] = "💀"  -- 致命
}

-- 主显示信息函数
function 显示信息(msg, 显示Toast, 级别, 附加信息)
    -- 参数处理
    if not msg then
        msg = "空消息"
    end
    
    -- 转换消息为字符串
    if type(msg) ~= "string" then
        msg = tostring(msg)
    end
    
    显示Toast = 显示Toast ~= false  -- 默认显示Toast
    级别 = 级别 or 日志级别.信息
    
    -- 检查是否应该显示此级别的消息
    if 级别 < 全局配置.最低显示级别 then
        return
    end
    
    -- 构建完整消息
    local 完整消息 = 构建消息(msg, 级别, 附加信息)
    
    -- 输出到控制台
    print(完整消息)
    
    -- 显示Toast（仅对警告及以上级别，或者明确要求显示）
    if 显示Toast and (级别 >= 日志级别.警告 or 显示Toast == true) then
        local toast消息 = msg
        if 全局配置.显示级别标识 then
            toast消息 = (级别颜色[级别] or "") .. " " .. msg
        end
        toast(toast消息)
    end
    
    -- 写入日志文件
    if 全局配置.启用日志文件 then
        写入日志文件(完整消息)
    end
end

-- 构建消息格式
function 构建消息(msg, 级别, 附加信息)
    local 消息部分 = {}
    
    -- 添加时间戳
    if 全局配置.显示时间戳 then
        table.insert(消息部分, string.format("[%s]", os.date("%Y-%m-%d %H:%M:%S")))
    end
    
    -- 添加级别标识
    if 全局配置.显示级别标识 then
        local 级别名称 = {
            [日志级别.调试] = "调试",
            [日志级别.信息] = "信息", 
            [日志级别.警告] = "警告",
            [日志级别.错误] = "错误",
            [日志级别.致命] = "致命"
        }
        local 图标 = 级别颜色[级别] or ""
        table.insert(消息部分, string.format("[%s%s]", 图标, 级别名称[级别] or "未知"))
    end
    
    -- 添加主消息
    table.insert(消息部分, msg)
    
    -- 添加附加信息
    if 附加信息 then
        if type(附加信息) == "table" then
            table.insert(消息部分, string.format("附加信息: %s", table.concat(附加信息, ", ")))
        else
            table.insert(消息部分, string.format("附加信息: %s", tostring(附加信息)))
        end
    end
    
    return table.concat(消息部分, " ")
end

-- 写入日志文件
function 写入日志文件(消息)
    -- 检查日志文件大小，如果过大则轮换
    轮换日志文件()
    
    -- 写入日志
    local 文件 = io.open(全局配置.日志文件路径, "a")
    if 文件 then
        文件:write(消息 .. "\n")
        文件:close()
    end
end

-- 日志文件轮换
function 轮换日志文件()
    local 文件 = io.open(全局配置.日志文件路径, "r")
    if not 文件 then return end
    
    -- 检查文件大小
    local 当前位置 = 文件:seek("end")
    文件:close()
    
    if 当前位置 > 全局配置.最大日志文件大小 then
        -- 备份当前日志文件
        local 备份文件名 = 全局配置.日志文件路径 .. "." .. os.date("%Y%m%d_%H%M%S")
        os.rename(全局配置.日志文件路径, 备份文件名)
        
        -- 清理旧的备份文件（保留最新的几个）
        清理旧日志文件()
    end
end

-- 清理旧日志文件
function 清理旧日志文件()
    -- 这里可以实现清理逻辑，删除过多的历史日志文件
    -- 由于Lua标准库功能有限，这里只是示例框架
end

-- 便捷函数：不同级别的显示
function 显示调试(msg, 显示Toast, 附加信息)
    显示信息(msg, 显示Toast, 日志级别.调试, 附加信息)
end

function 显示警告(msg, 显示Toast, 附加信息)
    显示信息(msg, 显示Toast or true, 日志级别.警告, 附加信息)
end

function 显示错误(msg, 显示Toast, 附加信息)
    显示信息(msg, 显示Toast or true, 日志级别.错误, 附加信息)
end

function 显示致命错误(msg, 显示Toast, 附加信息)
    显示信息(msg, 显示Toast or true, 日志级别.致命, 附加信息)
    -- 致命错误可能需要特殊处理，比如停止脚本
end

-- 配置函数
function 批量设置配置(配置表)
    if not 配置表 then return end
    
    for 键, 值 in pairs(配置表) do
        if 全局配置[键] ~= nil then
            全局配置[键] = 值
        end
    end
end

-- 启用/禁用日志文件
function 启用日志文件(文件路径)
    全局配置.启用日志文件 = true
    if 文件路径 then
        全局配置.日志文件路径 = 文件路径
    end
    显示信息("日志文件记录已启用: " .. 全局配置.日志文件路径)
end

function 禁用日志文件()
    全局配置.启用日志文件 = false
    显示信息("日志文件记录已禁用")
end

-- 设置显示级别
function 设置显示级别(级别)
    if 日志级别[级别] then
        全局配置.最低显示级别 = 日志级别[级别]
        显示信息("显示级别已设置为: " .. 级别)
    elseif type(级别) == "number" and 级别 >= 1 and 级别 <= 5 then
        全局配置.最低显示级别 = 级别
        显示信息("显示级别已设置为: " .. 级别)
    else
        显示错误("无效的显示级别: " .. tostring(级别))
    end
end

-- 批量显示（用于显示数组或表格数据）
function 批量显示(数据, 标题, 显示Toast)
    if 标题 then
        显示信息("=== " .. 标题 .. " ===", false)
    end
    
    if type(数据) == "table" then
        for 索引, 值 in pairs(数据) do
            local 消息 = string.format("[%s]: %s", tostring(索引), tostring(值))
            显示信息(消息, false)
        end
    else
        显示信息(tostring(数据), 显示Toast)
    end
    
    if 标题 then
        显示信息("=== 结束 ===", false)
    end
end

-- 显示进度信息
function 显示进度(当前, 总计, 描述, 显示Toast)
    local 百分比 = math.floor((当前 / 总计) * 100)
    local 进度条 = string.rep("█", math.floor(百分比 / 5)) .. string.rep("░", 20 - math.floor(百分比 / 5))
    local 消息 = string.format("%s [%s] %d%% (%d/%d)", 
        描述 or "进度", 进度条, 百分比, 当前, 总计)
    
    显示信息(消息, 显示Toast)
end

-- 显示执行时间
function 显示执行时间(开始时间, 操作描述, 显示Toast)
    local 结束时间 = os.clock()
    local 耗时 = 结束时间 - 开始时间
    local 消息 = string.format("%s 耗时: %.2f秒", 操作描述 or "操作", 耗时)
    显示信息(消息, 显示Toast)
    return 耗时
end

function 找图_区域找图(x1 , y1 , x2 , y2 , pic_name , delta_color , sim , dir , is_click)
	
	-- 参数有效性检查
	if not x1 or not y1 or not x2 or not y2 or not pic_name then
		return false , 0 , 0 , - 1
	end
	
	-- 设置参数默认值
	delta_color = delta_color or "000000" -- 默认偏色值
	sim = sim or 0.9 -- 默认相似度90%
	dir = dir or 0 -- 默认从左上往右下查找
	is_click = is_click or false -- 默认不点击
	
	-- 调用底层区域找图函数
	local ret , x , y = findPic(x1 , y1 , x2 , y2 , pic_name , delta_color , dir , sim)
	
	-- 处理查找结果
	if ret ~= - 1 then -- 找到图片
		-- 如果需要点击且找到了图片
		if is_click then
			tap(x , y) -- 点击找到的位置
		end
		return true , x , y , ret
	end
	
	-- 未找到图片
	return false , 0 , 0 , - 1
end

function 找图_快速区域找图(x1 , y1 , x2 , y2 , 图片名称 , 是否点击 , 偏色值 , 查找方向 , 相似度 , 点击延迟 , 随机偏移)
	-- 参数检查
	if not x1 or not y1 or not x2 or not y2 or not 图片名称 then
		显示信息("参数错误：坐标或图片名称不能为空")
		return false , - 1 , 0 , 0 , nil
	end
	
	-- 设置默认值
	是否点击 = 是否点击 or false
	偏色值 = 偏色值 or "000000"
	查找方向 = 查找方向 or 0
	相似度 = 相似度 or 1.0
	点击延迟 = 点击延迟 or 0
	随机偏移 = 随机偏移 or 0
	
	-- 调用底层快速找图函数
	local 索引 , 结果列表 = findPicFast(x1 , y1 , x2 , y2 , 图片名称 , 偏色值 , 查找方向 , 相似度)
	
	-- 处理返回结果
	if 索引 ~= - 1 and 结果列表 and #结果列表 > 0 then
		-- 找到图片，获取第一个结果的信息
		local 找到X = 结果列表[1].x
		local 找到Y = 结果列表[1].y
		
		-- 如果需要点击
		if 是否点击 then
			-- 计算点击坐标（带随机偏移）
			local 点击X = 找到X
			local 点击Y = 找到Y
			
			if 随机偏移 > 0 then
				点击X = 点击X + math.random( - 随机偏移 , 随机偏移)
				点击Y = 点击Y + math.random( - 随机偏移 , 随机偏移)
				
				-- 确保点击位置不超出查找区域
				点击X = math.max(x1 , math.min(x2 , 点击X))
				点击Y = math.max(y1 , math.min(y2 , 点击Y))
			end
			
			-- 等待指定时间
			if 点击延迟 > 0 then
				mSleep(点击延迟)
			end
			
			-- 执行点击
			tap(点击X , 点击Y)
			找到X = 点击X -- 返回实际点击的坐标
			找到Y = 点击Y
		end
		
		return true , 索引 , 找到X , 找到Y , 结果列表
	end
	
	-- 未找到图片
	return false , - 1 , 0 , 0 , nil
end

function 打开网址(url , timeout , retry_times)
	-- 参数检查和默认值设置
	if not url then
		显示信息("错误：未提供网址")
		return false
	end
	
	timeout = timeout or 30 -- 默认30秒超时
	retry_times = retry_times or 3 -- 默认重试3次
	
	-- 确保URL格式正确
	if not string.match(url , "^https?://") then
		url = "http://" .. url
	end
	
	-- 重试循环
	for i = 1 , retry_times do
		显示信息(string.format("正在尝试打开网页（第%d次）: %s" , i , url))
		
		-- 尝试打开网页
		-- 使用 os.execute 和 am start 命令来打开网址，这在安卓环境中更通用
		local command = string.format("am start -a android.intent.action.VIEW -d '%s'", url)
		local success = os.execute(command)

		if not success then
			显示信息("打开网页失败，正在重试...")
			mSleep(2000) -- 失败后等待2秒再重试
			goto continue
		end
		
		-- 等待网页加载
		local start_time = os.time()
		while (os.time() - start_time) < timeout do
			-- 检查是否加载完成 (isWebpageLoaded 可能也不存在，暂时注释掉)
			-- if isWebpageLoaded() then
				显示信息("网页加载成功")
				return true
			-- end
			-- mSleep(1000) -- 每秒检查一次
		end
		
		显示信息("网页加载超时")
		
		::continue::
	end
	
	显示信息(string.format("打开网页失败，已重试%d次: %s" , retry_times , url))
	return false
end

function Base64加密(str)
	-- 参数检查
	if not str then
		显示信息("错误：未提供要加密的字符串")
		return nil
	end
	
	-- 调用底层Base64加密函数
	local result = lua_Base64加密(str)
	if not result then
		显示信息("Base64加密失败")
		return nil
	end
	
	return result
end

function Base64解密(base64_str)
	-- 参数检查
	if not base64_str then
		显示信息("错误：未提供要解密的Base64字符串")
		return nil
	end
	
	-- 检查是否是有效的Base64字符串
	if not string.match(base64_str , "^[A-Za-z0-9+/=]+$") then
		显示信息("错误：无效的Base64字符串格式")
		return nil
	end
	
	-- 调用底层Base64解密函数
	local result = lua_Base64解密(base64_str)
	if not result then
		显示信息("Base64解密失败")
		return nil
	end
	
	return result
end

function 下载文件(url , save_path , show_progress , timeout)
	-- 参数检查
	if not url or not save_path then
		显示信息("错误：URL或保存路径不能为空")
		return false
	end
	
	-- 设置默认值
	show_progress = show_progress or true
	timeout = timeout or 60
	
	-- 检查URL类型
	local is_ftp = string.match(url , "^ftp://")
	local is_http = string.match(url , "^https?://")
	
	if not (is_ftp or is_http) then
		显示信息("错误：不支持的URL格式，仅支持HTTP和FTP")
		return false
	end
	
	-- 显示下载信息
	显示信息(string.format("开始下载文件：\nURL: %s\n保存路径: %s" , url , save_path))
	
	-- 创建保存目录（如果不存在）
	local save_dir = string.match(save_path , "(.*)/[^/]*$")
	if save_dir then
		os.execute("mkdir -p " .. save_dir)
	end
	
	-- 开始下载
	local start_time = os.time()
	local success = lua_下载文件(url , save_path , show_progress)
	
	-- 检查下载结果
	if success then
		local used_time = os.time() - start_time
		显示信息(string.format("下载完成！用时：%d秒" , used_time))
		
		-- 检查文件是否存在且大小大于0
		local file = io.open(save_path , "r")
		if file then
			file:close()
			return true
		else
			显示信息("错误：文件下载可能不完整")
			return false
		end
	else
		显示信息("下载失败")
		return false
	end
end

function 函数加密(func)
	-- 参数检查
	if type(func) ~= "function" then
		显示信息("错误：参数必须是函数")
		return nil
	end
	
	-- 调用底层加密函数
	local result = CompileFunc(func)
	if not result then
		显示信息("函数加密失败")
		return nil
	end
	
	return result
end

function 函数解密(encrypted_str)
	-- 参数检查
	if not encrypted_str then
		显示信息("错误：加密字符串不能为空")
		return nil
	end
	
	-- 调用底层解密函数
	local result = lua_函数解密(encrypted_str)
	if not result then
		显示信息("函数解密失败")
		return nil
	end
	
	return result
end

function 判断虚拟机()
	-- 调用底层检测函数
	local result = lua_判断虚拟机()
	
	-- 检测结果处理
	if result then
		显示信息("检测到虚拟机环境")
		
		-- 获取虚拟机类型
		local vm_type = ""
		if string.find(result , "VMware") then
			vm_type = "VMware"
		elseif string.find(result , "VirtualBox") then
			vm_type = "VirtualBox"
		elseif string.find(result , "Parallel") then
			vm_type = "Parallels"
		elseif string.find(result , "模拟器") then
			vm_type = "安卓模拟器"
		else
			vm_type = "未知虚拟机"
		end
		
		return true , vm_type
	else
		显示信息("当前为物理机环境")
		return false , nil
	end
end

function 虚拟机检测带重试(重试次数)
	重试次数 = 重试次数 or 3
	
	for i = 1 , 重试次数 do
		显示信息(string.format("第%d次检测..." , i))
		
		local 是否虚拟机 , 虚拟机类型 = 判断虚拟机()
		if 是否虚拟机 then
			return true , 虚拟机类型
		end
		
		if i < 重试次数 then
			显示信息("等待后重试...")
			mSleep(1000) -- 等待1秒后重试
		end
	end
	
	return false , nil
end

function 文件是否存在(file_path)
	-- 参数检查
	if not file_path then
		显示信息("错误：文件路径不能为空")
		return false
	end
	
	-- 调用底层函数检查文件是否存在
	local result = lua_文件判断是否存在(file_path)
	
	-- 返回检查结果
	return result
end

function 获取文件MD5(file_path)
	-- 参数检查
	if not file_path then
		显示信息("错误：文件路径不能为空")
		return nil
	end
	
	-- 检查文件是否存在
	if not 文件是否存在(file_path) then
		显示信息("错误：文件不存在：" .. file_path)
		return nil
	end
	
	-- 获取文件MD5值
	local md5 = lua_文件获取md5值(file_path)
	return md5
end

function 获取文件大小(file_path)
	-- 参数检查
	if not file_path then
		显示信息("错误：文件路径不能为空")
		return nil
	end
	
	-- 检查文件是否存在
	if not 文件是否存在(file_path) then
		显示信息("错误：文件不存在：" .. file_path)
		return nil
	end
	
	-- 获取文件大小
	local size = lua_文件获取字节大小(file_path)
	return size
end

function 文件完整性检查(file_path , expected_md5 , expected_size)
	-- 参数检查
	if not file_path then
		显示信息("错误：文件路径不能为空")
		return false
	end
	
	-- 检查文件是否存在
	if not 文件是否存在(file_path) then
		显示信息("错误：文件不存在：" .. file_path)
		return false
	end
	
	-- 检查文件大小
	if expected_size then
		local size = 获取文件大小(file_path)
		if not size or size ~= expected_size then
			显示信息(string.format("文件大小不匹配：期望%d字节，实际%d字节" , expected_size , size or 0))
			return false
		end
	end
	
	-- 检查MD5值
	if expected_md5 then
		local md5 = 获取文件MD5(file_path)
		if not md5 or md5 ~= expected_md5 then
			显示信息("文件MD5值不匹配")
			return false
		end
	end
	
	return true
end

function 时间_获取当前时间()
	local 当前时间 = os.date("*t")
	return {
		年 = 当前时间.year ,
		月 = 当前时间.month ,
		日 = 当前时间.day ,
		时 = 当前时间.hour ,
		分 = 当前时间.min ,
		秒 = 当前时间.sec ,
		星期 = 当前时间.wday -- 1是星期天，2是星期一，以此类推
	}
end

function 时间_获取小时()
	return os.date("*t").hour
end

function 时间_获取分钟()
	return os.date("*t").min
end

function 时间_格式化(格式)
	格式 = 格式 or "%Y-%m-%d %H:%M:%S"
	return os.date(格式)
end

function 时间_判断时间段(开始小时 , 结束小时)
	local 当前小时 = 时间_获取小时()
	return 当前小时 >= 开始小时 and 当前小时 < 结束小时
end

function 时间_等待到指定时间(目标小时 , 目标分钟)
	while true do
		local 当前小时 = 时间_获取小时()
		local 当前分钟 = 时间_获取分钟()
		
		if 当前小时 == 目标小时 and 当前分钟 == 目标分钟 then
			显示信息("到达指定时间")
			break
		end
		
		mSleep(60000) -- 每分钟检查一次
	end
end

function 字符串_转数字(字符串 , 默认值)
	默认值 = 默认值 or 0
	if not 字符串 then return 默认值 end
	
	-- 尝试转换
	local 数字 = tonumber(字符串)
	if 数字 then
		return 数字
	end
	
	return 默认值
end

function 字符串_分割(字符串 , 分隔符)
	if not 字符串 then return {} end
	分隔符 = 分隔符 or ","
	
	local 结果 = {}
	for 匹配 in string.gmatch(字符串 .. 分隔符 , "(.-)" .. 分隔符) do
		table.insert(结果 , 匹配)
	end
	return 结果
end

function 字符串_替换(字符串 , 查找内容 , 替换内容 , 替换次数)
	if not 字符串 or not 查找内容 then return 字符串 end
	替换内容 = 替换内容 or ""
	
	if 替换次数 then
		return string.gsub(字符串 , 查找内容 , 替换内容 , 替换次数)
	else
		return string.gsub(字符串 , 查找内容 , 替换内容)
	end
end

function 字符串_查找(字符串 , 查找内容 , 起始位置)
	if not 字符串 or not 查找内容 then return nil end
	return string.find(字符串 , 查找内容 , 起始位置 , true)
end

function 字符串_截取(字符串 , 起始位置 , 结束位置)
	if not 字符串 then return "" end
	return string.sub(字符串 , 起始位置 , 结束位置)
end

function 字符串_去空格(字符串 , 位置)
	if not 字符串 then return "" end
	位置 = 位置 or "两端"
	
	if 位置 == "左" or 位置 == "两端" then
		字符串 = string.gsub(字符串 , "^%s+" , "")
	end
	if 位置 == "右" or 位置 == "两端" then
		字符串 = string.gsub(字符串 , "%s+$" , "")
	end
	return 字符串
end

function 字符串_是否为空(字符串 , 是否去空格)
	if not 字符串 then return true end
	if 是否去空格 then
		字符串 = 字符串_去空格(字符串)
	end
	return 字符串 == ""
end

function 数组_创建(大小 , 默认值)
	local 新数组 = {}
	if 大小 then
		for i = 1 , 大小 do
			新数组[i] = 默认值
		end
	end
	return 新数组
end

function 数组_长度(数组)
	if not 数组 then return 0 end
	return #数组
end

function 数组_添加(数组 , ...)
	if not 数组 then return 0 end
	local 参数列表 = {...}
	for _ , 值 in ipairs(参数列表) do
		table.insert(数组 , 值)
	end
	return #数组
end

function 数组_删除(数组 , 位置)
	if not 数组 or #数组 == 0 then return nil end
	return table.remove(数组 , 位置)
end

function 数组_查找(数组 , 查找值 , 起始位置)
	if not 数组 then return nil end
	起始位置 = 起始位置 or 1
	
	for i = 起始位置 , #数组 do
		if 数组[i] == 查找值 then
			return i
		end
	end
	return nil
end

function 数组_排序(数组 , 比较函数)
	if not 数组 then return end
	table.sort(数组 , 比较函数)
end

function 数组_反转(数组)
	if not 数组 then return {} end
	local 新数组 = {}
	for i = #数组 , 1 , - 1 do
		table.insert(新数组 , 数组[i])
	end
	return 新数组
end

function 数组_合并(数组1 , ...)
	local 结果 = {}
	
	-- 复制第一个数组
	if 数组1 then
		for _ , v in ipairs(数组1) do
			table.insert(结果 , v)
		end
	end
	
	-- 合并其他数组
	local 其他数组 = {...}
	for _ , 数组 in ipairs(其他数组) do
		if 数组 then
			for _ , v in ipairs(数组) do
				table.insert(结果 , v)
			end
		end
	end
	
	return 结果
end

function 数组_去重(数组)
	if not 数组 then return {} end
	
	local 结果 = {}
	local 已存在 = {}
	
	for _ , v in ipairs(数组) do
		if not 已存在[v] then
			table.insert(结果 , v)
			已存在[v] = true
		end
	end
	
	return 结果
end

function 延迟(毫秒)
	-- 参数检查
	if not 毫秒 then
		毫秒 = 1000 -- 默认延迟1秒
	end
	
	-- 确保参数为数字
	毫秒 = tonumber(毫秒)
	if not 毫秒 then
		显示信息("延迟参数必须是数字")
		return false
	end
	
	-- 执行延迟
	sleep(毫秒)
	return true
end

function 找色_区域多点找色(x1 , y1 , x2 , y2 , first_color , offset_color , dir , sim , is_click)
	-- 参数检查
	if not x1 or not y1 or not x2 or not y2 or not first_color then
		显示信息("参数错误：坐标或颜色值不能为空")
		return false , - 1 , - 1
	end
	
	-- 设置默认值
	dir = dir or 0
	sim = sim or 0.9
	is_click = is_click or false
	
	-- 调用底层多点找色函数
	local x , y = findMultiColor(x1 , y1 , x2 , y2 , first_color , offset_color , dir , sim)
	
	-- 处理返回结果
	if x ~= - 1 and y ~= - 1 then
		-- 找到了指定颜色
		if is_click then
			-- 点击找到的位置
			tap(x , y)
		end
		return true , x , y
	end
	
	-- 未找到
	return false , - 1 , - 1
end


-- ==================== 统一错误处理机制 ====================

function 记录错误(函数名, 错误信息)
    local 时间戳 = os.date("%Y-%m-%d %H:%M:%S")
    local 完整信息 = string.format("[%s] %s: %s", 时间戳, 函数名, 错误信息)
    
    if 全局配置.错误日志 then
        显示信息("❌ " .. 完整信息)
    end
end

function 记录调试(函数名, 调试信息)
    if 全局配置.调试模式 then
        local 时间戳 = os.date("%H:%M:%S")
        显示信息(string.format("🔍 [%s] %s: %s", 时间戳, 函数名, 调试信息))
    end
end

-- ==================== 性能监控 ====================

function 性能_计时开始(标签)
    if not 全局配置.性能监控 then return end
    _G["计时_" .. 标签] = os.clock()
    记录调试("性能监控", "开始计时：" .. 标签)
end

function 性能_计时结束(标签)
    if not 全局配置.性能监控 then return 0 end
    
    local 开始时间 = _G["计时_" .. 标签]
    if 开始时间 then
        local 耗时 = os.clock() - 开始时间
        记录调试("性能监控", string.format("%s 耗时: %.3f秒", 标签, 耗时))
        _G["计时_" .. 标签] = nil
        return 耗时
    end
    return 0
end

-- ==================== 智能找图功能 ====================

function 找图_智能找图(x1, y1, x2, y2, 图片名称, 选项)
    -- 参数检查
    if not x1 or not y1 or not x2 or not y2 or not 图片名称 then
        记录错误("找图_智能找图", "坐标或图片名称不能为空")
        return false, -1, 0, 0, nil
    end
    
    -- 设置默认选项
    选项 = 选项 or {}
    local 偏色值 = 选项.偏色值 or 获取配置("默认偏色值")
    local 相似度 = 选项.相似度 or 获取配置("默认相似度")
    local 查找方向 = 选项.查找方向 or 获取配置("默认查找方向")
    local 是否点击 = 选项.是否点击 or false
    local 使用快速模式 = 选项.使用快速模式 or false
    local 点击延迟 = 选项.点击延迟 or 0
    local 随机偏移 = 选项.随机偏移 or 0
    local 最大重试次数 = 选项.最大重试次数 or 1
    
    性能_计时开始("智能找图_" .. 图片名称)
    记录调试("找图_智能找图", string.format("查找图片：%s，模式：%s", 图片名称, 使用快速模式 and "快速" or "普通"))
    
    -- 重试循环
    for 重试次数 = 1, 最大重试次数 do
        local 成功, 索引, x, y, 结果列表
        
        if 使用快速模式 then
            成功, 索引, x, y, 结果列表 = 找图_快速区域找图(x1, y1, x2, y2, 图片名称, 是否点击, 偏色值, 查找方向, 相似度, 点击延迟, 随机偏移)
        else
            成功, x, y, 索引 = 找图_区域找图(x1, y1, x2, y2, 图片名称, 偏色值, 相似度, 查找方向, 是否点击)
        end
        
        if 成功 then
            性能_计时结束("智能找图_" .. 图片名称)
            记录调试("找图_智能找图", string.format("找到图片，坐标：(%d,%d)", x, y))
            return 成功, 索引, x, y, 结果列表
        end
        
        if 重试次数 < 最大重试次数 then
            记录调试("找图_智能找图", string.format("第%d次查找失败，准备重试", 重试次数))
            mSleep(500) -- 重试间隔
        end
    end
    
    性能_计时结束("智能找图_" .. 图片名称)
    记录调试("找图_智能找图", string.format("查找失败，已重试%d次", 最大重试次数))
    return false, -1, 0, 0, nil
end

-- ==================== 截图保存功能 ====================

function 截图_保存到文件(x1, y1, x2, y2, 文件路径, 质量)
    if not 文件路径 then
        记录错误("截图_保存到文件", "文件路径不能为空")
        return false
    end
    
    质量 = 质量 or 90
    
    -- 获取屏幕尺寸
    local 屏幕宽度, 屏幕高度 = getScreenSize()
    
    -- 设置默认截图区域为全屏
    x1 = x1 or 0
    y1 = y1 or 0
    x2 = x2 or 屏幕宽度
    y2 = y2 or 屏幕高度
    
    记录调试("截图_保存到文件", string.format("截图区域：(%d,%d,%d,%d)", x1, y1, x2, y2))
    
    -- 执行截图
    local 成功 = snapshot(文件路径, x1, y1, x2, y2, 质量)
    
    if 成功 then
        记录调试("截图_保存到文件", "截图保存成功：" .. 文件路径)
    else
        记录错误("截图_保存到文件", "截图保存失败")
    end
    
    return 成功
end

-- ==================== 颜色处理功能 ====================

function 颜色_是否相似(颜色1, 颜色2, 容差)
    if not 颜色1 or not 颜色2 then
        记录错误("颜色_是否相似", "颜色值不能为空")
        return false
    end
    
    容差 = 容差 or 30
    
    -- 解析颜色值
    local function 解析颜色(颜色字符串)
        if string.len(颜色字符串) ~= 6 then
            return nil
        end
        
        local r = tonumber(string.sub(颜色字符串, 1, 2), 16)
        local g = tonumber(string.sub(颜色字符串, 3, 4), 16)
        local b = tonumber(string.sub(颜色字符串, 5, 6), 16)
        
        return r, g, b
    end
    
    local r1, g1, b1 = 解析颜色(颜色1)
    local r2, g2, b2 = 解析颜色(颜色2)
    
    if not r1 or not r2 then
        记录错误("颜色_是否相似", "颜色格式错误")
        return false
    end
    
    -- 计算颜色差值
    local 差值 = math.sqrt((r1-r2)^2 + (g1-g2)^2 + (b1-b2)^2)
    
    return 差值 <= 容差
end

function 颜色_获取点颜色(x, y)
    if not x or not y then
        记录错误("颜色_获取点颜色", "坐标不能为空")
        return nil
    end
    
    local 颜色值 = getColor(x, y)
    return string.format("%06X", 颜色值)
end

-- ==================== 坐标工具功能 ====================

function 坐标_随机偏移(x, y, 偏移范围, 边界检查)
    if not x or not y then
        记录错误("坐标_随机偏移", "坐标不能为空")
        return x or 0, y or 0
    end
    
    偏移范围 = 偏移范围 or 5
    
    local 新x = x + math.random(-偏移范围, 偏移范围)
    local 新y = y + math.random(-偏移范围, 偏移范围)
    
    -- 边界检查
    if 边界检查 and #边界检查 >= 4 then
        新x = math.max(边界检查[1], math.min(边界检查[3], 新x))
        新y = math.max(边界检查[2], math.min(边界检查[4], 新y))
    end
    
    return 新x, 新y
end

function 坐标_计算距离(x1, y1, x2, y2)
    if not x1 or not y1 or not x2 or not y2 then
        记录错误("坐标_计算距离", "坐标参数不完整")
        return 0
    end
    
    return math.sqrt((x2-x1)^2 + (y2-y1)^2)
end

function 坐标_点在区域内(x, y, x1, y1, x2, y2)
    if not x or not y or not x1 or not y1 or not x2 or not y2 then
        记录错误("坐标_点在区域内", "参数不完整")
        return false
    end
    
    return x >= x1 and x <= x2 and y >= y1 and y <= y2
end

