---
---
--- 函数名称：Base64加密
--- 功能说明：将字符串进行Base64加密
--- 参数说明：
---     str：要加密的字符串
--- 返回值：加密后的Base64字符串，如果加密失败返回nil
---
--- [查看文档](command:extension.lua.doc?[Base64加密])
---
--- @param ... any
function Base64加密()
    -- TODO: Implement this function
end

---
--- 函数名称：Base64解密
--- 功能说明：将Base64字符串解密
--- 参数说明：
---     base64_str：要解密的Base64字符串
--- 返回值：解密后的原始字符串，如果解密失败返回nil
---
--- [查看文档](command:extension.lua.doc?[Base64解密])
---
--- @param ... any
function Base64解密()
    -- TODO: Implement this function
end

---
--- 函数名称：下载文件
--- 功能说明：下载HTTP或FTP文件到指定路径
--- 参数说明：
---     url：下载链接（支持http和ftp）
---     save_path：保存路径
---     show_progress：是否显示下载进度，默认true
---     timeout：超时时间（秒），默认60秒
--- 返回值：是否下载成功
--- 使用示例：
---     下载文件("http://www.example.com/file.txt", "/sdcard/file.txt")
---     下载文件("ftp://user:<EMAIL>/file.txt", "/sdcard/file.txt")
---
--- [查看文档](command:extension.lua.doc?[下载文件])
---
--- @param ... any
function 下载文件()
    -- TODO: Implement this function
end

---
--- 函数名称：函数加密
--- 功能说明：将lua函数进行加密
--- 参数说明：
---     func：要加密的函数
--- 返回值：加密后的字符串，失败返回nil
---
--- [查看文档](command:extension.lua.doc?[函数加密])
---
--- @param ... any
function 函数加密()
    -- TODO: Implement this function
end

---
--- 
---
--- [查看文档](command:extension.lua.doc?[函数解密])
---
--- @param ... any
function 函数解密()
    -- TODO: Implement this function
end

---
--- 函数名称：判断虚拟机
--- 功能说明：检测当前环境是否为虚拟机
--- 参数说明：无
--- 返回值：
---     - 是否虚拟机：true表示是虚拟机，false表示是物理机
---     - 虚拟机类型：如果是虚拟机，返回具体类型（VMware/VirtualBox/Parallels/安卓模拟器/未知虚拟机）
--- 支持的虚拟机类型：
---     - VMware
---     - VirtualBox
---     - Parallels
---     - 安卓模拟器
---     - 其他未知类型
--- 使用示例：
---     local 是否虚拟机, 虚拟机类型 = 判断虚拟机()
---     if 是否虚拟机 then
---         显示信息("检测到" .. 虚拟机类型)
---     end
---
--- [查看文档](command:extension.lua.doc?[判断虚拟机])
---
--- @param ... any
function 判断虚拟机()
    -- TODO: Implement this function
end

---
--- 函数名称：字符串_分割
--- 功能说明：将字符串按指定分隔符分割成数组
--- 参数说明：
---     字符串：要分割的字符串
---     分隔符：分割字符，默认为逗号
--- 返回值：分割后的字符串数组
--- 
---
--- [查看文档](command:extension.lua.doc?[字符串_分割])
---
--- @param ... any
function 字符串_分割()
    -- TODO: Implement this function
end

---
--- 函数名称：字符串_去空格
--- 功能说明：去除字符串首尾的空格
--- 参数说明：
---     字符串：要处理的字符串
---     位置：去除位置，可选值："两端"(默认)、"左"、"右"
--- 返回值：处理后的字符串
---
--- [查看文档](command:extension.lua.doc?[字符串_去空格])
---
--- @param ... any
function 字符串_去空格()
    -- TODO: Implement this function
end

---
--- 函数名称：字符串_截取
--- 功能说明：截取字符串的指定部分
--- 参数说明：
---     字符串：要截取的字符串
---     起始位置：开始位置（可以为负数，-1表示最后一个字符）
---     结束位置：结束位置（可选，默认到字符串末尾）
--- 返回值：截取后的字符串
---
--- [查看文档](command:extension.lua.doc?[字符串_截取])
---
--- @param ... any
function 字符串_截取()
    -- TODO: Implement this function
end

---
--- 函数名称：字符串_是否为空
--- 功能说明：判断字符串是否为空（nil或空字符串）
--- 参数说明：
---     字符串：要判断的字符串
---     是否去空格：是否去除空格后再判断，默认false
--- 返回值：true/false
--- 
---
--- [查看文档](command:extension.lua.doc?[字符串_是否为空])
---
--- @param ... any
function 字符串_是否为空()
    -- TODO: Implement this function
end

---
--- 函数名称：字符串_替换
--- 功能说明：替换字符串中的指定内容
--- 参数说明：
---     字符串：原字符串
---     查找内容：要替换的内容
---     替换内容：替换后的内容
---     替换次数：替换次数，默认全部替换
--- 返回值：替换后的字符串
---
--- [查看文档](command:extension.lua.doc?[字符串_替换])
---
--- @param ... any
function 字符串_替换()
    -- TODO: Implement this function
end

---
--- 函数名称：字符串_查找
--- 功能说明：在字符串中查找指定内容
--- 参数说明：
---     字符串：要搜索的字符串
---     查找内容：要查找的内容
---     起始位置：开始查找的位置，可选
--- 返回值：
---     成功：返回找到的起始位置和结束位置
---     失败：返回nil
---
--- [查看文档](command:extension.lua.doc?[字符串_查找])
---
--- @param ... any
function 字符串_查找()
    -- TODO: Implement this function
end

---
--- 函数名称：字符串_转数字
--- 功能说明：将字符串转换为数字，支持整数、小数、科学计数法
--- 参数说明：
---     字符串：要转换的字符串
---     默认值：转换失败时返回的默认值，可选，默认为0
--- 返回值：
---     成功：返回转换后的数字
---     失败：返回默认值
---
--- [查看文档](command:extension.lua.doc?[字符串_转数字])
---
--- @param ... any
function 字符串_转数字()
    -- TODO: Implement this function
end

---
--- 描述：
---     使脚本暂停执行指定的时间
--- 
--- 参数：
---     毫秒 - 要延迟的毫秒数（可选，默认1000毫秒）
---     
--- 返回值：
---     boolean - 延迟执行成功返回true，参数错误返回false
---
--- [查看文档](command:extension.lua.doc?[延迟])
---
--- @param ... any
function 延迟()
    -- TODO: Implement this function
end

---
--- 函数名称：打开网址
--- 功能说明：打开指定的网址并等待加载完成
--- 参数说明：
---     url：要打开的网址
---     timeout：等待加载超时时间（秒），默认30秒
---     retry_times：重试次数，默认3次
--- 返回值：是否成功打开网页
--- 使用示例：
---     打开网址("https://www.baidu.com")
---     打开网址("https://www.google.com", 60, 5)  -- 60秒超时，最多重试5次
---
--- [查看文档](command:extension.lua.doc?[打开网址])
---
--- @param ... any
function 打开网址()
    -- TODO: Implement this function
end

---
--- 描述：
---     在指定区域内查找指定图片，可选择是否点击找到的位置
--- 
--- 
--- 参数：
---     x1, y1 - 查找区域左上角坐标
---     x2, y2 - 查找区域右下角坐标
---     pic_name - 要查找的图片文件名（需要包含在资源目录中）
---     delta_color - 偏色值，默认"000000"
---     sim - 相似度，范围0-1，默认0.9
---     dir - 查找方向，默认0（从左上往右下）
---         0: 从左上往右下查找
---         1: 从左下往右上查找
---         2: 从右上往左下查找
---         3: 从右下往左上查找
---     is_click - 是否点击找到的位置，默认false
--- 
--- 
--- 返回值：
---     成功找到图片时返回：true, x坐标, y坐标, 匹配索引
---     未找到图片时返回：false, 0, 0, -1
---
--- [查看文档](command:extension.lua.doc?[找图_区域找图])
---
--- @param ... any
function 找图_区域找图()
    -- TODO: Implement this function
end

---
--- 描述：
---     快速在指定区域内查找指定图片，支持更多高级选项，如随机偏移点击和延迟
--- 
--- 参数：
---     x1, y1 - 查找区域左上角坐标
---     x2, y2 - 查找区域右下角坐标
---     图片名称 - 要查找的图片文件名
---     是否点击 - 是否点击找到的位置，默认false
---     偏色值 - 偏色值，默认"000000"
---     查找方向 - 查找方向，默认0（同上）
---     相似度 - 相似度，范围0-1，默认1.0
---     点击延迟 - 点击后等待时间（毫秒），默认0
---     随机偏移 - 点击位置随机偏移量，默认0
--- 
--- 返回值：
---     成功找到图片时返回：true, 匹配索引, x坐标, y坐标, 结果列表
---     未找到图片时返回：false, -1, 0, 0, nil
---
--- [查看文档](command:extension.lua.doc?[找图_快速区域找图])
---
--- @param ... any
function 找图_快速区域找图()
    -- TODO: Implement this function
end

---
--- 描述：
---     在指定区域内查找指定的多个颜色点，可选择是否点击找到的位置
--- 
--- 参数：
---     x1, y1 - 查找区域左上角坐标
---     x2, y2 - 查找区域右下角坐标
---     first_color - 要对比的16进制颜色，多个颜色用"|"分隔
---                  支持偏色设置，用"-"分隔，如"888888|123456-000000|00FF00-101010"
---     offset_color - 偏移颜色
---     dir - 查找方向，默认0
---         0: 从左上向右下查找
---         1: 从中心往四周查找
---         2: 从右下向左上查找
---         3: 从左下向右上查找
---         4: 从右上向左下查找
---     sim - 相似度，范围0-1，默认0.9
---     is_click - 是否点击找到的位置，默认false
--- 
--- 返回值：
---     成功找到时返回：true, x坐标, y坐标
---     未找到时返回：false, -1, -1
--- 
---
--- [查看文档](command:extension.lua.doc?[找色_区域多点找色])
---
--- @param ... any
function 找色_区域多点找色()
    -- TODO: Implement this function
end

---
--- 函数名称：数组_创建
--- 功能说明：创建一个新数组，可以指定初始大小和默认值
--- 参数说明：
---     大小：数组的初始大小（可选）
---     默认值：数组元素的默认值（可选）
--- 返回值：新创建的数组
---
--- [查看文档](command:extension.lua.doc?[数组_创建])
---
--- @param ... any
function 数组_创建()
    -- TODO: Implement this function
end

---
--- 函数名称：数组_删除
--- 功能说明：删除数组中指定位置的元素
--- 参数说明：
---     数组：目标数组
---     位置：要删除的元素位置（可选，默认删除最后一个元素）
--- 返回值：被删除的元素（如果删除失败返回nil）
---
--- [查看文档](command:extension.lua.doc?[数组_删除])
---
--- @param ... any
function 数组_删除()
    -- TODO: Implement this function
end

---
--- 函数名称：数组_去重
--- 功能说明：去除数组中的重复元素
--- 参数说明：
---     数组：要处理的数组
--- 返回值：去重后的新数组（原数组不变）
---
--- [查看文档](command:extension.lua.doc?[数组_去重])
---
--- @param ... any
function 数组_去重()
    -- TODO: Implement this function
end

---
--- 函数名称：数组_反转
--- 功能说明：反转数组元素的顺序
--- 参数说明：
---     数组：要反转的数组
--- 返回值：反转后的新数组（原数组不变）
---
--- [查看文档](command:extension.lua.doc?[数组_反转])
---
--- @param ... any
function 数组_反转()
    -- TODO: Implement this function
end

---
--- 函数名称：数组_合并
--- 功能说明：合并两个或多个数组
--- 参数说明：
---     数组1：第一个数组
---     ...: 要合并的其他数组
--- 返回值：合并后的新数组（原数组不变）
---
--- [查看文档](command:extension.lua.doc?[数组_合并])
---
--- @param ... any
function 数组_合并()
    -- TODO: Implement this function
end

---
--- 函数名称：数组_排序
--- 功能说明：对数组进行排序
--- 参数说明：
---     数组：要排序的数组
---     比较函数：自定义比较函数（可选，默认为升序）
--- 返回值：无（直接修改原数组）
---
--- [查看文档](command:extension.lua.doc?[数组_排序])
---
--- @param ... any
function 数组_排序()
    -- TODO: Implement this function
end

---
--- 函数名称：数组_查找
--- 功能说明：在数组中查找指定元素，返回第一个匹配的位置
--- 参数说明：
---     数组：要搜索的数组
---     查找值：要查找的元素值
---     起始位置：开始查找的位置（可选，默认从1开始）
--- 返回值：
---     找到：返回元素的位置（数字）
---     未找到：返回nil
---
--- [查看文档](command:extension.lua.doc?[数组_查找])
---
--- @param ... any
function 数组_查找()
    -- TODO: Implement this function
end

---
--- 函数名称：数组_添加
--- 功能说明：向数组末尾添加一个或多个元素
--- 参数说明：
---     数组：目标数组
---     ...: 要添加的一个或多个元素
--- 返回值：添加后的数组长度
---
--- [查看文档](command:extension.lua.doc?[数组_添加])
---
--- @param ... any
function 数组_添加()
    -- TODO: Implement this function
end

---
--- 函数名称：数组_长度
--- 功能说明：获取数组的长度
--- 参数说明：
---     数组：要获取长度的数组
--- 返回值：数组的长度（如果输入无效则返回0）
---
--- [查看文档](command:extension.lua.doc?[数组_长度])
---
--- @param ... any
function 数组_长度()
    -- TODO: Implement this function
end

---
--- 函数名称：文件完整性检查
--- 功能说明：检查文件的MD5值和大小是否符合预期
--- 参数说明：
---     file_path：文件路径
---     expected_md5：预期的MD5值（可选）
---     expected_size：预期的文件大小（可选）
--- 返回值：
---     - true：检查通过
---     - false：检查失败
---
--- [查看文档](command:extension.lua.doc?[文件完整性检查])
---
--- @param ... any
function 文件完整性检查()
    -- TODO: Implement this function
end

---
--- 函数名称：文件是否存在
--- 功能说明：判断指定路径的文件是否存在
--- 参数说明：
---     file_path：文件路径（支持绝对路径和相对路径）
--- 返回值：
---     - true：文件存在
---     - false：文件不存在
--- 使用示例：
---     if 文件是否存在("/sdcard/test.png") then
---         显示信息("文件存在")
---     end
---
--- [查看文档](command:extension.lua.doc?[文件是否存在])
---
--- @param ... any
function 文件是否存在()
    -- TODO: Implement this function
end

---
--- 函数名称：时间_判断时间段
--- 功能说明：判断当前时间是否在指定时间段内
--- 参数说明：
---     开始小时：起始小时（0-23）
---     结束小时：结束小时（0-23）
--- 返回值：true/false
--- 
---
--- [查看文档](command:extension.lua.doc?[时间_判断时间段])
---
--- @param ... any
function 时间_判断时间段()
    -- TODO: Implement this function
end

---
--- 函数名称：时间_格式化
--- 功能说明：按指定格式返回时间字符串
--- 参数说明：
---     格式：时间格式字符串，例如"%Y-%m-%d %H:%M:%S"
---         %Y：年份（如2024）
---         %m：月份（01-12）
---         %d：日期（01-31）
---         %H：小时（00-23）
---         %M：分钟（00-59）
---         %S：秒钟（00-59）
--- 返回值：格式化后的时间字符串
--- 
--- 
---
--- [查看文档](command:extension.lua.doc?[时间_格式化])
---
--- @param ... any
function 时间_格式化()
    -- TODO: Implement this function
end

---
--- 函数名称：时间_等待到指定时间
--- 功能说明：等待直到达到指定的时间
--- 参数说明：
---     目标小时：要等待的小时（0-23）
---     目标分钟：要等待的分钟（0-59）
--- 
--- 
---
--- [查看文档](command:extension.lua.doc?[时间_等待到指定时间])
---
--- @param ... any
function 时间_等待到指定时间()
    -- TODO: Implement this function
end

---
--- 函数名称：时间_获取分钟
--- 功能说明：获取当前分钟数
--- 返回值：0-59之间的数字
--- 
---
--- [查看文档](command:extension.lua.doc?[时间_获取分钟])
---
--- @param ... any
function 时间_获取分钟()
    -- TODO: Implement this function
end

---
--- 函数名称：时间_获取小时
--- 功能说明：获取当前小时数（24小时制）
--- 返回值：0-23之间的数字
--- 
---
--- [查看文档](command:extension.lua.doc?[时间_获取小时])
---
--- @param ... any
function 时间_获取小时()
    -- TODO: Implement this function
end

---
--- 函数名称：时间_获取当前时间
--- 功能说明：获取完整的当前时间信息
--- 返回值：包含年月日时分秒星期的表
--- 
---
--- [查看文档](command:extension.lua.doc?[时间_获取当前时间])
---
--- @param ... any
function 时间_获取当前时间()
    -- TODO: Implement this function
end

---
--- 函数名称：获取文件MD5
--- 功能说明：获取指定文件的MD5值
--- 参数说明：
---     file_path：文件路径
--- 返回值：
---     - 成功返回文件的MD5值（32位小写字符串）
---     - 失败返回nil
---
--- [查看文档](command:extension.lua.doc?[获取文件MD5])
---
--- @param ... any
function 获取文件MD5()
    -- TODO: Implement this function
end

---
--- 函数名称：获取文件大小
--- 功能说明：获取指定文件的字节大小
--- 参数说明：
---     file_path：文件路径
--- 返回值：
---     - 成功返回文件大小（字节数）
---     - 失败返回nil
---
--- [查看文档](command:extension.lua.doc?[获取文件大小])
---
--- @param ... any
function 获取文件大小()
    -- TODO: Implement this function
end

---
--- 函数名称：虚拟机检测带重试
--- 功能说明：多次检测当前环境是否为虚拟机，提高准确性
--- 参数说明：
---     重试次数：整数，最多重试的次数（默认3次）
--- 返回值：
---     - 是否虚拟机：true表示是虚拟机，false表示是物理机
---     - 虚拟机类型：如果是虚拟机，返回具体类型
--- 使用示例：
---     local 是否虚拟机, 虚拟机类型 = 虚拟机检测带重试(5)  -- 5次重试
---
--- [查看文档](command:extension.lua.doc?[虚拟机检测带重试])
---
--- @param ... any
function 虚拟机检测带重试()
    -- TODO: Implement this function
end

