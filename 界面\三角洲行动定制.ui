<窗口
	宽度="720"
	高度="1280"
	显示确认按钮="true"
	显示标题栏="false"
	标题="阿萨拉卫兵定制"
	标签头高度="-2"
	标签头背景="#ffffffff"
	标签头字体大小="14"
	标签头选中字体颜色="#ff1c86ee"
	标签头字体默认颜色="#ff3355ff">
	
	<标签页 标题="选择功能" 背景="#ffffffff">
		<!-- 标题装饰 -->
		<文本框 id="LabelLines1" 默认值="                           " 文字大小="0" 高度="1" 宽度="-1" 边距="0,0,0,0" 背景="#FF1C86EE"/>
		<文本框 id="LabelLines2" 默认值="                         " 文字大小="0" 高度="1" 宽度="-1" 边距="0,0,0,0" 背景="#FF104E8B"/>
		<文本框 id="idLabel1" 默认值="阿萨拉卫兵功能配置" 文字大小="20" 高度="-2" 宽度="-1" 边距="0,5,0,5" 对齐="水平居中" 文字颜色="#FF104E8B"/>
		<文本框 id="LabelLines3" 默认值="                           " 文字大小="0" 高度="1" 宽度="-1" 边距="0,0,0,0" 背景="#FF1C86EE"/>
		<文本框 id="LabelLines4" 默认值="                         " 文字大小="0" 高度="1" 宽度="-1" 边距="0,0,0,0" 背景="#FF104E8B"/>
		
		<垂直布局 id="verticalLayout1" 宽度="-1" 高度="-1" 背景="#00222222">
			<!-- 使用说明 -->
			<文本框 id="usageInfo" 默认值="💡 每次运行请重新设置功能选项，点击确认后立即执行" 文字大小="14" 文字颜色="#ffffffff" 高度="-2" 宽度="-1" 边距="10,5,10,10" 对齐="水平居中" 背景="#FF32CD32"/>
			
			<文本框 id="textField2" 默认值="售后请找卡密售卖人" 文字大小="16" 文字颜色="#ff000000" 高度="-2" 宽度="-1" 边距="10,10,10,10" 对齐="左边对齐"/>
			
			<!-- 基础功能选择 -->
			<文本框 id="btnFunction" 默认值="======基础功能选择======" 文字大小="16" 高度="-2" 宽度="-1" 对齐="水平居中" 边距="0,0,0,10" 文字颜色="#ffffffff" 背景="#FF1C86EE"/>
			
			<水平布局 id="horizontalLayout1" 宽度="-1" 高度="-2" 背景="#00222222">
				<多选框 id="多选框_选择扫货功能" 选中="false" 文本="扫货功能" 文字大小="18" 文字颜色="#ff000000"/>
				<文本框 id="textField9" 默认值="需要抢购的商品放在收藏第一位" 文字大小="14" 文字颜色="#ff000000" 高度="-2" 宽度="-1" 边距="20,5,10,5"/>
			</水平布局>
			<水平布局 id="horizontalLayout1" 宽度="-1" 高度="-2" 背景="#00222222">
				<多选框 id="多选框_选择房卡功能" 选中="false" 文本="房卡抢购" 文字大小="18" 文字颜色="#ff000000"/>
				<文本框 id="textField9" 默认值="扫货和房卡功能二选一" 文字大小="14" 文字颜色="#ff000000" 高度="-2" 宽度="-1" 边距="20,5,10,5"/>
			</水平布局>
			            <水平布局 id="horizontalLayout_单机抢购" 宽度="-1" 高度="-2" 背景="#00222222">
                <多选框 id="多选框_单机抢购模式" 选中="false" 文本="单机抢购模式" 文字大小="18" 文字颜色="#ff000000"/>
                <文本框 id="textField_单机抢购说明" 默认值="只能12点以后价格正常运行" 文字大小="14" 文字颜色="#ff000000" 高度="-2" 宽度="-1" 边距="20,5,10,5"/>
            </水平布局>
			<水平布局 id="horizontalLayout1" 宽度="-1" 高度="-2" 背景="#00222222">
				<多选框 id="多选框_选择五级蛋" 选中="false" 文本="五级蛋和箭" 文字大小="18" 文字颜色="#ff000000"/>
				<文本框 id="textField9" 默认值="只用于五蛋和箭矢" 文字大小="14" 文字颜色="#ff000000" 高度="-2" 宽度="-1" 边距="20,5,10,5"/>
			</水平布局>
			<水平布局 id="horizontalLayout6" 宽度="-1" 高度="-2" 背景="#00222222">
				<多选框 id="多选框_出售子弹" 选中="false" 文本="出售子弹" 文字大小="18" 文字颜色="#ff000000"/>
				<下拉框
					id="子弹类型"
					宽度="120"
					高度="-2"
					边距="15,5,10,5"
					默认值="0">
					<选项 文本="9*19"/>
					<选项 文本="5.7*28"/>
					<选项 文本=".45 ACP"/>
					<选项 文本="12 Gauge"/>
					<选项 文本=".300 BLk"/>
				</下拉框>
				<文本框 id="textField8" 默认值="自动出售指定类型子弹" 文字大小="14" 文字颜色="#ff000000" 高度="-2" 宽度="-1" 边距="10,5,10,5"/>
			</水平布局>
			
			<文本框 id="textField10" 默认值="⚠️ 扫货和出售功能只能选择一个" 文字大小="16" 文字颜色="#ffffffff" 高度="-2" 宽度="-1" 边距="10,8,10,8" 对齐="水平居中" 背景="#FFFF6347"/>
			
			<!-- 全自动功能 -->
			<文本框 id="btnAuto" 默认值="======全自动功能======" 文字大小="16" 高度="-2" 宽度="-1" 对齐="水平居中" 边距="0,15,0,10" 文字颜色="#ffffffff" 背景="#FF1C86EE"/>
			
			<水平布局 id="horizontalLayout11" 宽度="-1" 高度="-2" 背景="#00222222">
				<多选框 id="扫货_自动挂机" 选中="false" 文本="全自动功能" 文字大小="18" 文字颜色="#ff000000"/>
				<文本框 id="textField14" 默认值="自动执行倒卖子弹流程" 文字大小="14" 文字颜色="#ff000000" 高度="-2" 宽度="-1" 边距="20,5,10,5"/>
			</水平布局>
			
			<!-- 特勤处功能 -->
			<文本框 id="btnSpecial" 默认值="======特勤处制作装备======" 文字大小="16" 高度="-2" 宽度="-1" 对齐="水平居中" 边距="0,15,0,10" 文字颜色="#ffffffff" 背景="#FF1C86EE"/>
			
			<水平布局 id="horizontalLayout23" 宽度="-1" 高度="-2" 背景="#00222222">
				<多选框 id="特勤处_制作装备" 选中="false" 文本="特勤处制作装备" 文字大小="18" 文字颜色="#ff000000"/>
				<文本框 id="textField24" 默认值="制作装备并自动领取" 文字大小="14" 文字颜色="#ff000000" 高度="-2" 宽度="-1" 边距="20,5,10,5"/>
			</水平布局>
			
			<文本框 id="textField24b" 默认值="💡 全自动制造需要同时勾选全自动和特勤处" 文字大小="16" 文字颜色="#ffffffff" 高度="-2" 宽度="-1" 边距="10,8,10,8" 对齐="水平居中" 背景="#FF4169E1"/>
			
			<!-- 简化使用说明 -->
			<文本框 id="btnUsage" 默认值="======使用说明======" 文字大小="16" 高度="-2" 宽度="-1" 对齐="水平居中" 边距="0,15,0,10" 文字颜色="#ffffffff" 背景="#FF9932CC"/>
			
			<文本框 id="usageTips" 默认值="✅ 选择需要的功能，配置价格参数后点击确认运行&#10;🔄 每次运行都需要重新设置，无配置保存功能&#10;⚙️ 简化版本避免了配置文件相关问题&#10;🔄 定时重启可清理游戏缓存，保持流畅运行" 文字大小="12" 文字颜色="#ff000000" 高度="-2" 宽度="-1" 边距="10,5,10,10" 对齐="左边对齐"/>
		</垂直布局>
	</标签页>
	
	<标签页 标题="子弹配置" 背景="#ffffffff">
		<!-- 标题装饰 -->
		<文本框 id="LabelLines5" 默认值="                           " 文字大小="0" 高度="1" 宽度="-1" 边距="0,0,0,0" 背景="#FF1C86EE"/>
		<文本框 id="LabelLines6" 默认值="                         " 文字大小="0" 高度="1" 宽度="-1" 边距="0,0,0,0" 背景="#FF104E8B"/>
		<文本框 id="idLabel2" 默认值="子弹价格参数配置" 文字大小="20" 高度="-2" 宽度="-1" 边距="0,5,0,5" 对齐="水平居中" 文字颜色="#FF104E8B"/>
		<文本框 id="LabelLines7" 默认值="                           " 文字大小="0" 高度="1" 宽度="-1" 边距="0,0,0,0" 背景="#FF1C86EE"/>
		<文本框 id="LabelLines8" 默认值="                         " 文字大小="0" 高度="1" 宽度="-1" 边距="0,0,0,0" 背景="#FF104E8B"/>
		
		<垂直布局 id="verticalLayout2" 宽度="-1" 高度="-1" 背景="#00222222">
			<!-- 配置说明 -->
			<文本框 id="configInfo2" 默认值="💡 设置价格参数，每次运行时都会使用当前设置的值" 文字大小="14" 文字颜色="#ffffffff" 高度="-2" 宽度="-1" 边距="10,5,10,10" 对齐="水平居中" 背景="#FF32CD32"/>
			
			<!-- 扫货价格配置 -->
			<文本框 id="btnSweep" 默认值="======扫货子弹价格配置======" 文字大小="12" 高度="-2" 宽度="-1" 对齐="水平居中" 边距="0,15,0,10" 文字颜色="#ffffffff" 背景="#FF1C86EE"/>
			
			<水平布局 id="horizontalLayout2" 宽度="-1" 高度="-2" 背景="#00222222">
				<文本框 id="textField5" 默认值="抢购子弹价格" 文字大小="12" 文字颜色="#ff000000" 高度="-2" 宽度="160" 边距="15,8,10,8"/>
				<输入框 id="输入框_扫货输入价格" 输入类型="1" 默认值="630" 文字大小="12" 宽度="80" 高度="-2" 边距="5,8,10,8" 提示文字="630"/>
				<文本框 id="textField5b" 默认值="低于此价格才购买" 文字大小="10" 文字颜色="#ff000000" 高度="-2" 宽度="-1" 边距="5,8,15,8"/>
			</水平布局>
			
			<水平布局 id="horizontalLayout3" 宽度="-1" 高度="-2" 背景="#00222222">
				<文本框 id="textField7" 默认值="点击抢购次数" 文字大小="12" 文字颜色="#ff000000" 高度="-2" 宽度="160" 边距="15,8,10,8"/>
				<输入框 id="输入框_抢购次数" 输入类型="1" 默认值="1" 文字大小="12" 宽度="80" 高度="-2" 边距="5,8,10,8" 提示文字="1"/>
				<文本框 id="textField7b" 默认值="连续点击次数" 文字大小="10" 文字颜色="#ff000000" 高度="-2" 宽度="-1" 边距="5,8,15,8"/>
			</水平布局>
			
			<水平布局 id="horizontalLayout_单机抢购价格" 宽度="-1" 高度="-2" 背景="#00222222">
				<文本框 id="textField_单机抢购价格" 默认值="单机抢购价格" 文字大小="12" 文字颜色="#ff000000" 高度="-2" 宽度="180" 边距="15,8,5,8"/>
				<输入框 id="输入框_单机抢购价格" 输入类型="1" 默认值="630" 文字大小="12" 宽度="80" 高度="-2" 边距="0,8,5,8" 提示文字="630"/>
				<文本框 id="textField_单机抢购延迟" 默认值="延迟" 文字大小="12" 文字颜色="#ff000000" 高度="-2" 宽度="70" 边距="5,8,0,8"/>
				<输入框 id="输入框_单机抢购延迟" 输入类型="1" 默认值="30" 文字大小="12" 宽度="40" 高度="-2" 边距="0,8,5,8" 提示文字="30"/>
				<文本框 id="textField_单机抢购价格_b" 默认值="秒" 文字大小="10" 文字颜色="#ff000000" 高度="-2" 宽度="-1" 边距="0,8,15,8"/>
			</水平布局>
			
			<水平布局 id="horizontalLayout_资金保护" 宽度="-1" 高度="-2" 背景="#00222222">
				<文本框 id="textField_资金保护" 默认值="资金保护阈值" 文字大小="12" 文字颜色="#ff000000" 高度="-2" 宽度="160" 边距="15,8,10,8"/>
				<输入框 id="输入框_资金保护" 输入类型="1" 默认值="500000" 文字大小="12" 宽度="80" 高度="-2" 边距="5,8,10,8" 提示文字="500000"/>
				<文本框 id="textField_资金保护_b" 默认值="余额低于此值暂停" 文字大小="10" 文字颜色="#ff000000" 高度="-2" 宽度="-1" 边距="5,8,15,8"/>
			</水平布局>
			
			<水平布局 id="horizontalLayout2" 宽度="-1" 高度="-2" 背景="#00222222">
				<文本框 id="textField5" 默认值="输入房卡材料价格" 文字大小="12" 文字颜色="#ff000000" 高度="-2" 宽度="160" 边距="15,8,10,8"/>
				<输入框 id="输入框_房卡输入价格" 输入类型="1" 默认值="" 文字大小="12" 宽度="80" 高度="-2" 边距="5,8,10,8" 提示文字="630"/>
				<文本框 id="textField5b" 默认值="低于此价格才购买" 文字大小="10" 文字颜色="#ff000000" 高度="-2" 宽度="-1" 边距="5,8,15,8"/>
			</水平布局>
			<水平布局 id="horizontalLayout_五级蛋" 宽度="-1" 高度="-2" 背景="#00222222">
				<文本框 id="textField_五级蛋" 默认值="输入五级蛋价格" 文字大小="12" 文字颜色="#ff000000" 高度="-2" 宽度="160" 边距="15,8,10,8"/>
				<输入框 id="输入框_五级蛋价格" 输入类型="1" 默认值="150000" 文字大小="12" 宽度="80" 高度="-2" 边距="5,8,10,8" 提示文字="150000"/>
				<文本框 id="textField_五级蛋_b" 默认值="低于此价格才购买" 文字大小="10" 文字颜色="#ff000000" 高度="-2" 宽度="-1" 边距="5,8,15,8"/>
			</水平布局>
			<!-- 出售价格配置 -->
			<文本框 id="btnSell" 默认值="======扫货出售价格设置======" 文字大小="12" 高度="-2" 宽度="-1" 对齐="水平居中" 边距="0,15,0,10" 文字颜色="#ffffffff" 背景="#FF1C86EE"/>
			
			<水平布局 id="horizontalLayout9" 宽度="-1" 高度="-2" 背景="#00222222">
				<文本框 id="textField12" 默认值="子弹出售价格" 文字大小="12" 文字颜色="#ff000000" 高度="-2" 宽度="160" 边距="15,8,10,8"/>
				<输入框 id="输入框_出售价格" 输入类型="1" 默认值="758" 文字大小="12" 宽度="80" 高度="-2" 边距="5,8,10,8" 提示文字="758"/>
				<文本框 id="textField12b" 默认值="每颗子弹售价" 文字大小="10" 文字颜色="#ff000000" 高度="-2" 宽度="-1" 边距="5,8,15,8"/>
			</水平布局>
			
			<!-- 时间设置 -->
			<文本框 id="btnTime" 默认值="======自定义时间设置======" 文字大小="12" 高度="-2" 宽度="-1" 对齐="水平居中" 边距="0,15,0,10" 文字颜色="#ffffffff" 背景="#FF1C86EE"/>
			
			<水平布局 id="horizontalLayout18" 宽度="-1" 高度="-2" 背景="#00222222">
				<文本框 id="textField19" 默认值="出售开始时间(小时)" 文字大小="12" 文字颜色="#ff000000" 高度="-2" 宽度="160" 边距="15,8,10,8"/>
				<输入框 id="出售_开始时间" 输入类型="1" 默认值="13" 文字大小="12" 宽度="80" 高度="-2" 边距="5,8,10,8" 提示文字="13"/>
				<文本框 id="textField19b" 默认值="24小时制" 文字大小="10" 文字颜色="#ff000000" 高度="-2" 宽度="-1" 边距="5,8,15,8"/>
			</水平布局>
			
			<水平布局 id="horizontalLayout19" 宽度="-1" 高度="-2" 背景="#00222222">
				<文本框 id="textField20" 默认值="出售结束时间(小时)" 文字大小="12" 文字颜色="#ff000000" 高度="-2" 宽度="160" 边距="15,8,10,8"/>
				<输入框 id="出售_结束时间" 输入类型="1" 默认值="19" 文字大小="12" 宽度="80" 高度="-2" 边距="5,8,10,8" 提示文字="19"/>
				<文本框 id="textField20b" 默认值="24小时制" 文字大小="10" 文字颜色="#ff000000" 高度="-2" 宽度="-1" 边距="5,8,15,8"/>
			</水平布局>
			
			<!-- 简化版说明 -->
			<文本框 id="btnSimplified" 默认值="======简化版说明======" 文字大小="12" 高度="-2" 宽度="-1" 对齐="水平居中" 边距="0,15,0,10" 文字颜色="#ffffffff" 背景="#FFFF4500"/>
			
			<文本框 id="simplifiedInfo" 默认值="🎯 本版本已移除配置保存功能&#10;✅ 每次运行都使用当前界面设置的参数&#10;🚀 避免了配置文件相关的兼容性问题&#10;🔄 定时重启功能已集成，可防止长时间运行导致的卡顿" 文字大小="11" 文字颜色="#ff000000" 高度="-2" 宽度="-1" 边距="10,5,10,10" 对齐="左边对齐"/>
		</垂直布局>
	</标签页>
</窗口>
