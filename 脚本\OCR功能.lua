-- OCR功能模块
-- 封装了OCR初始化和识别函数

local loader = LuaEngine.loadApk("/storage/emulated/0/Download/TomatoOCR.apk")

local OCR = loader.loadClass("com.tomato.ocr.lr.OCRApi")
local tmo_ocr = OCR.init(LuaEngine.getContext())

local license = "XLLCXLQMGSQRM6UG7OPHX40YD6E7AO9K|rFkWHeO5hZKEJ4XsJJOi4ZVI";-- 请确保这里的License正确
local flag = tmo_ocr.setLicense(license);-- 设置license，见授权码获取

-- OCR识别函数（用于识别屏幕指定区域的数字）
-- 参数：x1，y1，x2，y2（识别区域坐标）、zi（预留参数，当前未使用）
-- 返回：识别到的数字字符串
local function ocr_start(x1, y1, x2, y2, zi)
 -- 设置OCR参数，优化速度
 tmo_ocr.setRecType("ch-3.0") -- 中文识别模型
 tmo_ocr.setReturnType("num") -- 仅返回数字
 tmo_ocr.setRunMode("fast") -- 快速模式（牺牲部分精度换速度）
 tmo_ocr.setRecScoreThreshold(0.8) -- 识别置信度阈值（0.8以上认为有效）
 
local type = 2 -- 使用只识别模式（不做区域检测，直接识别）
 
-- 截取屏幕区域为bitmap并识别
 local bitmap = LuaEngine.snapShot(x1, y1, x2, y2) -- 截图
 local result = tmo_ocr.ocrBitmap(bitmap, type) -- 执行OCR
 bitmap.recycle() -- 释放bitmap资源
 
return result -- 返回识别结果
end

local function ocr_时间(x1, y1, x2, y2, zi)
 -- 设置OCR参数，优化速度
 tmo_ocr.setRecType("ch-3.0") -- 中文识别模型
 tmo_ocr.setReturnType("0123456789:") -- 仅返回数字
 tmo_ocr.setRunMode("fast") -- 快速模式（牺牲部分精度换速度）
 tmo_ocr.setRecScoreThreshold(0.8) -- 识别置信度阈值（0.8以上认为有效）
 
local type = 2 -- 使用只识别模式（不做区域检测，直接识别）
 
-- 截取屏幕区域为bitmap并识别
 local bitmap = LuaEngine.snapShot(x1, y1, x2, y2) -- 截图
 local result = tmo_ocr.ocrBitmap(bitmap, type) -- 执行OCR
 bitmap.recycle() -- 释放bitmap资源
 
return result -- 返回识别结果
end

-- 返回一个包含OCR函数的表
return {
 ocr_start = ocr_start,
 ocr_时间 = ocr_时间
}