-- 自动领取邮件功能 (安全升级版)
-- 本函数自动查找并点击邮件相关按钮，循环领取所有邮件并安全返回大厅
-- 新增：超时机制和循环延迟，防止脚本卡死
function 自动领取邮件()
	local 开始时间 = os.time()
	local 超时秒数 = 180 -- 设置3分钟的超时时间

	toast("开始自动领取邮件...", 1000)

	while true do
		-- 检查是否超时
		if os.time() - 开始时间 > 超时秒数 then
			print("[自动领取邮件] 警告：领取邮件超时，为防止卡死已强制退出。")
			toast("领取邮件超时，强制退出", 2000)
			-- 尝试返回大厅作为恢复手段
			if _G.工具函数 and _G.工具函数.尝试返回大厅 then
				_G.工具函数.尝试返回大厅("邮件领取超时")
			end
			break
		end

		-- 1. 检查是否在交易行，优先返回大厅
		local index, x, y = findPic(0, 0, 163, 53, "领取邮件_交易行返回大厅.png|领取邮件_交易行返回大厅1.png", "101010", 0, 0.9)
		if x > -1 and y > -1 then
			print("[自动领取邮件] 找到'返回大厅'按钮，点击坐标：" .. x .. "," .. y)
			toast("返回大厅...", 1000)
			tap(x, y)
			sleep(1000)
		end
		
		-- 2. 查找邮件按钮
		index, x, y = findPic(1157, 3, 1279, 92, "领取邮件_点击邮件.png|领取邮件_点击邮件1.png|领取邮件_点击邮件2.png", "101010", 0, 0.9)
		if x > -1 and y > -1 then
			print("[自动领取邮件] 找到'邮件'按钮，点击坐标：" .. x .. "," .. y)
			toast("打开邮件...", 1000)
			tap(x, y)
			sleep(1000)
		end
		
		-- 3. 查找交易行邮件入口
		index, x, y = findPic(0, 0, 117, 413, "领取邮件_点击交易行.png|领取邮件_点击交易行1.png", "101010", 0, 0.9)
		if x > -1 and y > -1 then
			print("[自动领取邮件] 找到'交易行邮件'按钮，点击坐标：" .. x .. "," .. y)
			toast("进入交易行邮件...", 1000)
			tap(x, y)
			sleep(1000)
		end
		
		-- 4. 查找全部领取按钮
		index, x, y = findPic(191, 660, 265, 677, "领取邮件_全部领取.png|领取邮件_全部领取1.png|领取邮件_全部领取2.png", "101010", 0, 0.9)
		if x > -1 and y > -1 then
			print("[自动领取邮件] 找到'全部领取'按钮，点击坐标：" .. x .. "," .. y)
			toast("全部领取邮件...", 1000)
			tap(x, y)
			sleep(1000)
		end
		
		-- 5. 检查是否有领取完成提示
		index, x, y = findPic(517, 22, 775, 144, "领取完成.png|领取完成1.png", "101010", 0, 0.9)
		if x > -1 and y > -1 then
			print("[自动领取邮件] 找到'领取完成'提示，点击坐标：" .. x .. "," .. y)
			toast("关闭领取完成提示...", 1000)
			tap(x, y)
			sleep(5000)
		end
		
		-- 6. 检查是否有最终返回大厅按钮，安全退出循环
		index, x, y = findPic(0, 0, 150, 59, "领取邮件_返回大厅.png|领取邮件_返回大厅1.png", "101010", 0, 0.9)
		if x > -1 and y > -1 then
			print("[自动领取邮件] 找到'最终返回大厅'按钮，点击坐标：" .. x .. "," .. y)
			toast("领取完成，返回大厅...", 1000)
			tap(x, y)
			sleep(1000)
			print("[自动领取邮件] 成功返回大厅，领取流程结束")
			break
		end

		-- 在每次循环末尾增加短暂延迟，避免在找不到任何按钮时CPU空转
		sleep(1000)
	end
end

return 自动领取邮件