-- 声明依赖的外部函数

function 自动重启游戏()
	local 查找并点击 = _G.查找并点击
	local 显示信息 = _G.显示信息
	local 找图_区域找图 = _G.找图_区域找图
	
	local 重启开始时间 = os.time()
	local 重启超时时间 = 300 -- 5分钟超时
	
	显示信息("开始执行游戏重启流程...")
	
	-- 步骤1：安全停止应用
	local 停止状态, 停止错误 = pcall(function()
		stopApp("com.tencent.tmgp.dfm")
		显示信息("已发送停止应用指令，等待5秒...")
		sleep(5000)
	end)
	
	if not 停止状态 then
		显示信息("停止应用时发生错误: " .. tostring(停止错误))
	end
	
	-- 步骤2：安全启动应用
	local 启动状态, 启动错误 = pcall(function()
		runApp("com.tencent.tmgp.dfm")
		显示信息("已发送启动应用指令，等待应用加载...")
	end)
	
	if not 启动状态 then
		显示信息("启动应用时发生错误: " .. tostring(启动错误))
		return false
	end
	
	-- 步骤3：改进的登录处理逻辑
	local max_attempts = 120
	local attempt = 0
	local 成功进入大厅 = false
	
	while attempt < max_attempts do
		-- 检查重启超时
		if os.time() - 重启开始时间 > 重启超时时间 then
			显示信息("重启超时，强制结束重启流程")
			return false
		end
		
		attempt = attempt + 1
		
		-- 优先检查是否已在大厅
		local idx, x, y = findPic(1192, 0, 1279, 112, "到时间_返回大厅.png", "101010", 0, 0.7)
		if idx >= 0 then
			显示信息("✓✓✓ 成功进入游戏大厅，重启完成！")
			成功进入大厅 = true
			break
		end
		
		-- 使用pcall包装每个点击操作，避免单个操作失败影响整体流程
		pcall(查找并点击, 955, 616, 1279, 718, "重启游戏_开始游戏.png|重启游戏_开始游戏1.png", "101010", 0.8, 1000, "开始游戏按钮")
		pcall(查找并点击, 633, 441, 1012, 544, "重启游戏_关闭资源.png", "101010", 0.9, 1000, "关闭资源下载弹窗")
		
		-- 【修改】关闭活动弹窗的双重保险处理
		local 找到关闭活动, 活动x, 活动y = pcall(查找并点击, 1146, 0, 1279, 89, "重启游戏_关闭活动.png|重启游戏_关闭活动1.png", "303030", 0.8, 1000, "关闭活动弹窗")
		if 找到关闭活动 then
			显示信息("✓ 找到关闭活动资源，执行双重保险点击...")
			-- 双重保险：额外点击1202,45坐标
			tap(1202, 45)
			显示信息("✓ 双重保险点击完成 (1202,45)")
			sleep(500) -- 短暂等待确保点击生效
			
			-- 循环检测返回大厅按钮
			local 返回大厅尝试次数 = 0
			local 最大尝试次数 = 10
			local 找到返回按钮 = false
			
			while 返回大厅尝试次数 < 最大尝试次数 do
				返回大厅尝试次数 = 返回大厅尝试次数 + 1
				local index, x, y = findPic(0, 0, 150, 59, "领取邮件_返回大厅.png|领取邮件_返回大厅1.png", "101010", 0, 0.9)
				
				if x > -1 and y > -1 then
					显示信息("✓ 第" .. 返回大厅尝试次数 .. "次找到返回大厅按钮，点击返回")
					tap(x, y)
					找到返回按钮 = true
					sleep(1000)
					break
				else
					显示信息("第" .. 返回大厅尝试次数 .. "次未找到返回大厅按钮")
					sleep(500)
				end
			end
			
			if not 找到返回按钮 then
				显示信息("✓ 10次尝试后未找到返回大厅按钮，判断已在大厅")
			end
		end
		
		pcall(查找并点击, 1104, 85, 1213, 201, "重启_关闭重返行动.png", "151515", 0.8, 1000, "关闭重返行动弹窗")
		
		sleep(1000)
	end
	
	if not 成功进入大厅 then
		显示信息("!!! 重启失败：超时仍未进入游戏大厅")
		return false
	end
	
	return true
end

return 自动重启游戏
